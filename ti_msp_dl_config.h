/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)



#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_LED */
#define PWM_LED_INST                                                       TIMG6
#define PWM_LED_INST_IRQHandler                                 TIMG6_IRQHandler
#define PWM_LED_INST_INT_IRQN                                   (TIMG6_INT_IRQn)
#define PWM_LED_INST_CLK_FREQ                                            4000000
/* GPIO defines for channel 1 */
#define GPIO_PWM_LED_C1_PORT                                               GPIOB
#define GPIO_PWM_LED_C1_PIN                                       DL_GPIO_PIN_27
#define GPIO_PWM_LED_C1_IOMUX                                    (IOMUX_PINCM58)
#define GPIO_PWM_LED_C1_IOMUX_FUNC                   IOMUX_PINCM58_PF_TIMG6_CCP1
#define GPIO_PWM_LED_C1_IDX                                  DL_TIMER_CC_1_INDEX

/* Defines for PWM_MOTOR */
#define PWM_MOTOR_INST                                                     TIMG7
#define PWM_MOTOR_INST_IRQHandler                               TIMG7_IRQHandler
#define PWM_MOTOR_INST_INT_IRQN                                 (TIMG7_INT_IRQn)
#define PWM_MOTOR_INST_CLK_FREQ                                          4000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_MOTOR_C0_PORT                                             GPIOA
#define GPIO_PWM_MOTOR_C0_PIN                                     DL_GPIO_PIN_26
#define GPIO_PWM_MOTOR_C0_IOMUX                                  (IOMUX_PINCM59)
#define GPIO_PWM_MOTOR_C0_IOMUX_FUNC                 IOMUX_PINCM59_PF_TIMG7_CCP0
#define GPIO_PWM_MOTOR_C0_IDX                                DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_MOTOR_C1_PORT                                             GPIOA
#define GPIO_PWM_MOTOR_C1_PIN                                     DL_GPIO_PIN_27
#define GPIO_PWM_MOTOR_C1_IOMUX                                  (IOMUX_PINCM60)
#define GPIO_PWM_MOTOR_C1_IOMUX_FUNC                 IOMUX_PINCM60_PF_TIMG7_CCP1
#define GPIO_PWM_MOTOR_C1_IDX                                DL_TIMER_CC_1_INDEX



/* Defines for TIMER_0 */
#define TIMER_0_INST                                                     (TIMG0)
#define TIMER_0_INST_IRQHandler                                 TIMG0_IRQHandler
#define TIMER_0_INST_INT_IRQN                                   (TIMG0_INT_IRQn)
#define TIMER_0_INST_LOAD_VALUE                                            (39U)
/* Defines for TIMER_TICK */
#define TIMER_TICK_INST                                                  (TIMA0)
#define TIMER_TICK_INST_IRQHandler                              TIMA0_IRQHandler
#define TIMER_TICK_INST_INT_IRQN                                (TIMA0_INT_IRQn)
#define TIMER_TICK_INST_LOAD_VALUE                                       (7999U)



/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                            4000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                  (9600)
#define UART_0_IBRD_4_MHZ_9600_BAUD                                         (26)
#define UART_0_FBRD_4_MHZ_9600_BAUD                                          (3)





/* Port definition for Pin Group LED1 */
#define LED1_PORT                                                        (GPIOB)

/* Defines for PIN_22: GPIOB.22 with pinCMx 50 on package pin 21 */
#define LED1_PIN_22_PIN                                         (DL_GPIO_PIN_22)
#define LED1_PIN_22_IOMUX                                        (IOMUX_PINCM50)
/* Port definition for Pin Group KEY1 */
#define KEY1_PORT                                                        (GPIOB)

/* Defines for PIN_21: GPIOB.21 with pinCMx 49 on package pin 20 */
// groups represented: ["Encoder_2","KEY1"]
// pins affected: ["E2A","PIN_21"]
#define GPIO_MULTIPLE_GPIOB_INT_IRQN                            (GPIOB_INT_IRQn)
#define GPIO_MULTIPLE_GPIOB_INT_IIDX            (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define KEY1_PIN_21_IIDX                                    (DL_GPIO_IIDX_DIO21)
#define KEY1_PIN_21_PIN                                         (DL_GPIO_PIN_21)
#define KEY1_PIN_21_IOMUX                                        (IOMUX_PINCM49)
/* Port definition for Pin Group Software_iic */
#define Software_iic_PORT                                                (GPIOA)

/* Defines for SDA: GPIOA.1 with pinCMx 2 on package pin 34 */
#define Software_iic_SDA_PIN                                     (DL_GPIO_PIN_1)
#define Software_iic_SDA_IOMUX                                    (IOMUX_PINCM2)
/* Defines for SCL: GPIOA.0 with pinCMx 1 on package pin 33 */
#define Software_iic_SCL_PIN                                     (DL_GPIO_PIN_0)
#define Software_iic_SCL_IOMUX                                    (IOMUX_PINCM1)
/* Port definition for Pin Group MOTORA */
#define MOTORA_PORT                                                      (GPIOB)

/* Defines for AIN1: GPIOB.12 with pinCMx 29 on package pin 64 */
#define MOTORA_AIN1_PIN                                         (DL_GPIO_PIN_12)
#define MOTORA_AIN1_IOMUX                                        (IOMUX_PINCM29)
/* Defines for AIN2: GPIOB.13 with pinCMx 30 on package pin 1 */
#define MOTORA_AIN2_PIN                                         (DL_GPIO_PIN_13)
#define MOTORA_AIN2_IOMUX                                        (IOMUX_PINCM30)
/* Port definition for Pin Group MOTORB */
#define MOTORB_PORT                                                      (GPIOB)

/* Defines for BIN1: GPIOB.24 with pinCMx 52 on package pin 23 */
#define MOTORB_BIN1_PIN                                         (DL_GPIO_PIN_24)
#define MOTORB_BIN1_IOMUX                                        (IOMUX_PINCM52)
/* Defines for BIN2: GPIOB.25 with pinCMx 56 on package pin 27 */
#define MOTORB_BIN2_PIN                                         (DL_GPIO_PIN_25)
#define MOTORB_BIN2_IOMUX                                        (IOMUX_PINCM56)
/* Port definition for Pin Group Encoder_1 */
#define Encoder_1_PORT                                                   (GPIOA)

/* Defines for E1A: GPIOA.12 with pinCMx 34 on package pin 5 */
// pins affected by this interrupt request:["E1A"]
#define Encoder_1_INT_IRQN                                      (GPIOA_INT_IRQn)
#define Encoder_1_INT_IIDX                      (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define Encoder_1_E1A_IIDX                                  (DL_GPIO_IIDX_DIO12)
#define Encoder_1_E1A_PIN                                       (DL_GPIO_PIN_12)
#define Encoder_1_E1A_IOMUX                                      (IOMUX_PINCM34)
/* Defines for E1B: GPIOA.8 with pinCMx 19 on package pin 54 */
#define Encoder_1_E1B_PIN                                        (DL_GPIO_PIN_8)
#define Encoder_1_E1B_IOMUX                                      (IOMUX_PINCM19)
/* Port definition for Pin Group Encoder_2 */
#define Encoder_2_PORT                                                   (GPIOB)

/* Defines for E2A: GPIOB.4 with pinCMx 17 on package pin 52 */
#define Encoder_2_E2A_IIDX                                   (DL_GPIO_IIDX_DIO4)
#define Encoder_2_E2A_PIN                                        (DL_GPIO_PIN_4)
#define Encoder_2_E2A_IOMUX                                      (IOMUX_PINCM17)
/* Defines for E2B: GPIOB.5 with pinCMx 18 on package pin 53 */
#define Encoder_2_E2B_PIN                                        (DL_GPIO_PIN_5)
#define Encoder_2_E2B_IOMUX                                      (IOMUX_PINCM18)



/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_LED_init(void);
void SYSCFG_DL_PWM_MOTOR_init(void);
void SYSCFG_DL_TIMER_0_init(void);
void SYSCFG_DL_TIMER_TICK_init(void);
void SYSCFG_DL_UART_0_init(void);

void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
