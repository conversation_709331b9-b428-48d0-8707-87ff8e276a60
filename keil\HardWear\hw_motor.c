#include "hw_motor.h"

// 设置右电机速度
void set_motor_B(uint16_t dat)
{
	DL_GPIO_setPins(MOTORB_PORT, MOTORB_BIN1_PIN);
	DL_GPIO_clearPins(MOTORB_PORT, MOTORB_BIN2_PIN);
	DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, dat, GPIO_PWM_MOTOR_C1_IDX);
}

// 设置左电机速度
void set_motor_A(uint16_t dat)
{
	DL_GPIO_setPins(MOTORA_PORT, MOTORA_AIN2_PIN);
	DL_GPIO_clearPins(MOTORA_PORT, MOTORA_AIN1_PIN);
	DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, dat, GPIO_PWM_MOTOR_C0_IDX);
}

void set_motor(uint16_t left, uint16_t right)
{
	// 添加参数检查
	if (left > 1000)
		left = 1000;
	if (right > 1000)
		right = 1000;

	// 设置左电机方向 (MOTORA)
	DL_GPIO_setPins(MOTORA_PORT, MOTORA_AIN2_PIN);
	DL_GPIO_clearPins(MOTORA_PORT, MOTORA_AIN1_PIN);

	// 设置右电机方向 (MOTORB)
	DL_GPIO_setPins(MOTORB_PORT, MOTORB_BIN2_PIN);
	DL_GPIO_clearPins(MOTORB_PORT, MOTORB_BIN1_PIN);

	// 设置PWM值：左电机->C1通道，右电机->C0通道
	DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, left, GPIO_PWM_MOTOR_C1_IDX);
	DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, right, GPIO_PWM_MOTOR_C0_IDX);

	// 添加小延时确保设置生效
	__asm("nop");
	__asm("nop");
	__asm("nop");
}
// 电机停止
void stop_motor(void)
{
	set_motor(999, 999);
}