#include "hw_encoder.h"

static ENCODER_RES Left_encoder;
static ENCODER_RES Right_encoder;

// 调试计数器
static volatile uint32_t left_interrupt_count = 0;
static volatile uint32_t right_interrupt_count = 0;

/*********使能编码电机外部中断*********/
void encoder_init(void)
{
    NVIC_ClearPendingIRQ(Encoder_1_INT_IRQN);
    NVIC_ClearPendingIRQ(GPIOB_INT_IRQn);

    NVIC_EnableIRQ(Encoder_1_INT_IRQN);
    NVIC_EnableIRQ(GPIOB_INT_IRQn);
}

/*********获取左编码器的值***********/
int get_left_encoder_count(void)
{
    return Left_encoder.count;
}

ENCODER_DIR get_left_encoder_dir(void)
{
    return Left_encoder.dir;
}

void left_encoder_update(void)
{
    Left_encoder.count = Left_encoder.temp_count;
    // 确定方向
    Left_encoder.dir = (Left_encoder.count >= 0) ? FORWARD : REVERSAL;

    Left_encoder.temp_count = 0; // 编码器计数值清零
}

/*********获取右编码器的值***********/
int get_right_encoder_count(void)
{
    return Right_encoder.count;
}

ENCODER_DIR get_right_encoder_dir(void)
{
    return Right_encoder.dir;
}

void right_encoder_update(void)
{
    Right_encoder.count = Right_encoder.temp_count;
    // 确定方向
    Right_encoder.dir = (Right_encoder.count >= 0) ? FORWARD : REVERSAL;

    Right_encoder.temp_count = 0; // 编码器计数值清零
}

/*********调试函数：获取临时计数值***********/
long long get_left_encoder_temp_count(void)
{
    return Left_encoder.temp_count;
}

long long get_right_encoder_temp_count(void)
{
    return Right_encoder.temp_count;
}

/*********编码器复位函数***********/
void reset_encoders(void)
{
    Left_encoder.count = 0;
    Left_encoder.temp_count = 0;
    Left_encoder.dir = FORWARD;

    Right_encoder.count = 0;
    Right_encoder.temp_count = 0;
    Right_encoder.dir = FORWARD;

    // 复位调试计数器
    left_interrupt_count = 0;
    right_interrupt_count = 0;
}

/*********获取中断计数（调试用）***********/
uint32_t get_left_interrupt_count(void)
{
    return left_interrupt_count;
}

uint32_t get_right_interrupt_count(void)
{
    return right_interrupt_count;
}

// 外部中断处理函数
// GPIOA中断处理函数 - 处理左编码器
void GPIOA_IRQHandler(void)
{
    // 快速处理，避免中断时间过长
    uint32_t gpio_status_A = DL_GPIO_getEnabledInterruptStatus(Encoder_1_PORT, Encoder_1_E1A_PIN);

    // 左编码器A相上升沿触发
    if ((gpio_status_A & Encoder_1_E1A_PIN) == Encoder_1_E1A_PIN)
    {
        left_interrupt_count++; // 调试计数器

        // 简化逻辑，减少中断处理时间
        if (DL_GPIO_readPins(Encoder_1_PORT, Encoder_1_E1B_PIN) > 0)
        {
            Left_encoder.temp_count++;
        }
        else
        {
            Left_encoder.temp_count--;
        }

        // 立即清除中断状态
        DL_GPIO_clearInterruptStatus(Encoder_1_PORT, Encoder_1_E1A_PIN);
    }
}

// GPIOB中断处理函数 - 处理右编码器和按键
void GPIOB_IRQHandler(void)
{
    // 快速处理，避免中断时间过长
    uint32_t gpio_status_B = DL_GPIO_getEnabledInterruptStatus(Encoder_2_PORT, Encoder_2_E2A_PIN);

    // 右编码器A相上升沿触发
    if ((gpio_status_B & Encoder_2_E2A_PIN) == Encoder_2_E2A_PIN)
    {
        right_interrupt_count++; // 调试计数器

        // 简化逻辑，减少中断处理时间
        if (DL_GPIO_readPins(Encoder_2_PORT, Encoder_2_E2B_PIN) > 0)
        {
            Right_encoder.temp_count++;
        }
        else
        {
            Right_encoder.temp_count--;
        }

        // 立即清除编码器中断状态
        DL_GPIO_clearInterruptStatus(Encoder_2_PORT, Encoder_2_E2A_PIN);
    }

    // 清除所有其他GPIOB中断状态（包括按键等）
    DL_GPIO_clearInterruptStatus(GPIOB, 0xFFFFFFFF);
}