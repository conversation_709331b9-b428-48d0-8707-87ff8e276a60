#include "hw_encoder.h"

static ENCODER_RES Left_encoder;
static ENCODER_RES Right_encoder;

	/*********使能编码电机外部中断*********/
void encoder_init(void)
{
	NVIC_ClearPendingIRQ(Encoder_1_INT_IRQN);
	NVIC_ClearPendingIRQ(GPIOB_INT_IRQn);

	NVIC_EnableIRQ(Encoder_1_INT_IRQN);
	NVIC_EnableIRQ(GPIOB_INT_IRQn);
}

/*********获取左编码器的值***********/
int get_left_encoder_count(void)
{
	return Left_encoder.count;
}

ENCODER_DIR get_left_encoder_dir(void)
{
	return Left_encoder.dir;
}

void left_encoder_update(void)
{
	Left_encoder.count = Left_encoder.temp_count;
	//确定方向
	Left_encoder.dir = ( Left_encoder.count >= 0 ) ? FORWARD : REVERSAL;

	Left_encoder.temp_count = 0;//编码器计数值清零
}

/*********获取右编码器的值***********/
int get_right_encoder_count(void)
{
	return Right_encoder.count;
}

ENCODER_DIR get_right_encoder_dir(void)
{
	return Right_encoder.dir;
}

void right_encoder_update(void)
{
	Right_encoder.count = Right_encoder.temp_count;
	//确定方向
	Right_encoder.dir = ( Right_encoder.count >= 0 ) ? FORWARD : REVERSAL;

	Right_encoder.temp_count = 0;//编码器计数值清零
}

//外部中断处理函数
void GROUP1_IRQHandler(void)
{
	uint32_t gpio_status_A;
	uint32_t gpio_status_B;
	//获取中断信号情况
	gpio_status_A = DL_GPIO_getEnabledInterruptStatus(Encoder_1_PORT, Encoder_1_E1A_PIN);
	gpio_status_B = DL_GPIO_getEnabledInterruptStatus(Encoder_2_PORT, Encoder_2_E2A_PIN);

	//编码器A相上升沿触发
	if((gpio_status_A & Encoder_1_E1A_PIN) == Encoder_1_E1A_PIN)
	{
		//如果在A相上升沿下，B相为低电平
		if(DL_GPIO_readPins(Encoder_1_PORT,Encoder_1_E1B_PIN) > 0)
		{
			Left_encoder.temp_count--;
		}
		else
		{
			Left_encoder.temp_count++;
		}
	}
	
	DL_GPIO_clearInterruptStatus(Encoder_1_PORT, Encoder_1_E1A_PIN);
	
	
	if((gpio_status_B & Encoder_2_E2A_PIN) == Encoder_2_E2A_PIN)
	{
	
		if(DL_GPIO_readPins(Encoder_2_PORT,Encoder_2_E2B_PIN) > 0)
		{
			Right_encoder.temp_count++;
		}
		else
		{
			Right_encoder.temp_count--;
		}
		
	}
	
	DL_GPIO_clearInterruptStatus(Encoder_2_PORT, Encoder_2_E2A_PIN);
	//清除状态
}