/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/************************* ͷ�ļ� *************************/
#include "ti_msp_dl_config.h"
#include "software_iic.h"
#include "stdio.h"
#include "string.h"

#include "LED.h"
#include "KEY.h"
#include "USART.h"
#include "PWM.h"
#include "hw_motor.h"
#include "hw_encoder.h"
#include "timer.h"

#if !defined(__MICROLIB)
// ��ʹ��΢��Ļ�����Ҫ��������ĺ���
#if (__ARMCLIB_VERSION <= 6000000)
// �����������AC5  �Ͷ�����������ṹ��

struct __FILE
{
	int handle;
};

#endif
FILE __stdout;
// ����_sys_exit()�Ա���ʹ�ð�����ģʽ
void _sys_exit(int x)
{
	x = x;
}
#endif

/************************* �궨�� *************************/
#define delay_ms(X) delay_cycles((CPUCLK_FREQ / 1000) * (X));

/* Ĭ�ϵ�ַ */
#define GW_GRAY_ADDR_DEF 0x4C
#define GW_GRAY_PING 0xAA
#define GW_GRAY_PING_OK 0x66
#define GW_GRAY_PING_RSP GW_GRAY_PING_OK

/* ������������ģʽ */
#define GW_GRAY_DIGITAL_MODE 0xDD

/* ����������ȡģ������ģʽ */
#define GW_GRAY_ANALOG_BASE_ 0xB0
#define GW_GRAY_ANALOG_MODE (GW_GRAY_ANALOG_BASE_ + 0)
/************************ �������� ************************/

volatile unsigned int delay_times = 0;
extern uint8_t IIC_ReadBytes(uint8_t Salve_Address, uint8_t Reg_Address,
							 uint8_t *Result, uint8_t len);
/*
ȫ�ֱ���
*/
uint8_t rx_buff[256] = {0};		  // uart1��ӡ�õģ����ùܣ���Ҫ��
uint8_t IIC_write_buff[10] = {0}; // IICд�����õ�buff
uint8_t Anolog[8] = {0};		  // 1-8·ģ��������
uint8_t Normal[8] = {0};		  // ��һ�����1-8·ģ��������
uint8_t Digtal;

/************************ �������� ************************/
void PWM_LED(void);

int main(void)
{
	SYSCFG_DL_init();

	// 添加启动延时，确保系统稳定
	delay_ms(1000);

	timer0_init();
	tim_tick_init();
	//    //ʹ���ⲿ�ж�
	//		NVIC_EnableIRQ (KEY1_INT_IRQN );
	Uart_init();

	// 添加串口初始化延时
	delay_ms(500);

	printf("=== SYSTEM STARTUP ===\r\n");
	delay_ms(100);

	// 暂时不启用编码器中断，先测试电机
	// encoder_init();

	// 复位编码器计数
	// reset_encoders();

	printf("Encoder interrupts DISABLED for testing\r\n");
	delay_ms(100);

	printf("System initialized. Starting motor test...\r\n");
	delay_ms(100);
	printf("Step 1: Motors OFF - Testing system stability\r\n");
	delay_ms(100);

	// 先测试不启动电机的情况
	set_motor(0, 0);
	delay_ms(100);

	printf("No motor test 1 - System stable\r\n");
	delay_ms(500);
	printf("No motor test 2 - System stable\r\n");
	delay_ms(500);
	printf("No motor test 3 - System stable\r\n");
	delay_ms(500);

	printf("Step 2: Starting motors at very low speed...\r\n");
	delay_ms(100);

	printf("About to call set_motor(100, 100)...\r\n");
	delay_ms(100);
	set_motor(100, 100);
	delay_ms(100);
	printf("set_motor(100, 100) completed\r\n");
	delay_ms(100);

	// 监控低速运行状态
	printf("Low speed test 1 - Motor running\r\n");
	delay_ms(500);
	printf("Low speed test 2 - Motor running\r\n");
	delay_ms(500);
	printf("Low speed test complete\r\n");
	delay_ms(100);

	printf("Step 3: Medium speed test...\r\n");
	delay_ms(100);
	printf("About to call set_motor(300, 300)...\r\n");
	delay_ms(100);
	set_motor(300, 300);
	delay_ms(100);
	printf("set_motor(300, 300) completed\r\n");
	delay_ms(100);

	// 监控中速运行状态
	printf("Medium speed test 1 - Motor running\r\n");
	delay_ms(500);
	printf("Medium speed test 2 - Motor running\r\n");
	delay_ms(500);
	printf("Medium speed test complete\r\n");
	delay_ms(100);

	printf("Step 4: Target speed...\r\n");
	delay_ms(100);
	printf("About to call set_motor(500, 500)...\r\n");
	delay_ms(100);
	set_motor(500, 500);
	delay_ms(100);
	printf("set_motor(500, 500) completed\r\n");
	delay_ms(100);

	printf("Motors started. Simple monitoring...\r\n");
	delay_ms(100);

	static uint32_t loop_count = 0;

	while (1)
	{
		loop_count++;

		// 简化的心跳指示
		printf("[HEARTBEAT %lu] System running - Motors at 500\r\n", loop_count);
		delay_ms(1000); // 1秒间隔，减少输出

		// 每5次心跳停止电机测试
		if (loop_count >= 5)
		{
			printf("Test complete. Stopping motors.\r\n");
			set_motor(0, 0);
			delay_ms(1000);
			printf("Motors stopped. Test finished.\r\n");
			break;
		}
	}

	// 测试完成后的无限循环
	while (1)
	{
		printf("Test finished. System idle.\r\n");
		delay_ms(5000);
	}

	//		sprintf((char *)rx_buff,"hello_world!\r\n");
	//		uart0_send_string((char *)rx_buff);
	//		memset(rx_buff,0,256);
	//		while(Ping())
	//		{
	//			delay_ms(1);
	//			sprintf((char *)rx_buff,"Ping Faild Try Again!\r\n");
	//			uart0_send_string((char *)rx_buff);
	//			memset(rx_buff,0,256);
	//		}
	//		sprintf((char *)rx_buff,"Ping Succseful!\r\n");
	//		uart0_send_string((char *)rx_buff);
	//		memset(rx_buff,0,256);
	//		while (1)
	//		{
	//			/*������*/
	//			/*���������ݺ�1-8��ָʾ����ͬ��������ҪУ׼������������ȡ*/
	//			/*��������ȡ����1���ȽϷ�ʱ�����������⣬��������Ч�����뿴�ĵ�*/
	//			IIC_ReadBytes(GW_GRAY_ADDR_DEF << 1/* Ĭ�ϲ���AD0 AD1��ַ0x4c */, GW_GRAY_DIGITAL_MODE/* 0xDD */, &Digtal/*���ݴ����Digtal��*/, 1 /*ֻ��1������*/);
	//
	//			/*ģ����*/
	//			/*����ͨ��ģ������ȡ��У׼�޹�*/
	//			/*��ͬһ���׳��£�ģ�������ݻ��������죬�������������������Ҫ������ͬ����У׼��򿪹�һ��*/
	//			/*����ͨ��ģ������ȡ����1���ȽϷ�ʱ�����������⣬��������Ч�����뿴�ĵ�*/
	//			IIC_ReadBytes(GW_GRAY_ADDR_DEF << 1, GW_GRAY_ANALOG_MODE/* 0xB0 */, &Anolog[0] /*���ݴ����Anolog��*/, 8 /*��8������*/);
	//
	//
	//			/*��һ��*/
	//			/*��һ������˼�ǣ�ʹ����̽ͷ����ͬһ����ɫ���ߺ�ɫ�£�������һ�µ�*/
	//			/*�����У׼���йصģ�����ͨ��У׼���ݸ�ģ������������������һ����*/
	//			IIC_write_buff[0]=GW_GRAY_ANALOG_NORMALIZE;//��һ��ʹ�ܼĴ��������粻�洢
	//			IIC_write_buff[1]=0xff;//ȫͨ������
	//			IIC_WriteBytes(GW_GRAY_ADDR_DEF << 1,GW_GRAY_ANALOG_NORMALIZE ,&IIC_write_buff[1]/*����+����*/, 2 /*д����������*/);
	//			delay_ms(10);//�����꣬��Ҫ����һ�ᡣstm8�������ٶ�ûstm32�죬��һ�£��ô�����������ˢ��һ�¡�
	//			IIC_ReadBytes(GW_GRAY_ADDR_DEF << 1, GW_GRAY_ANALOG_MODE, &Normal[0]/*�򿪹�һ������������ݴ���Normalize��*/ , 8 );//����������ͨ��ģ������ȡһ��
	//			IIC_write_buff[0]=GW_GRAY_ANALOG_NORMALIZE;
	//			IIC_write_buff[1]=0x00;//ȫͨ���ر�
	//			IIC_WriteBytes(GW_GRAY_ADDR_DEF << 1,GW_GRAY_ANALOG_NORMALIZE,&IIC_write_buff[1]/*����+����*/, 2 /*д����������*/);//Ϊ��while(1)ѭ����������Ȼ��Ҫ�ص���һ���ġ�
	//
	//			delay_ms(10);
	//			/*��ӡ����*/
	//			sprintf((char *)rx_buff,"Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
	//			uart0_send_string((char *)rx_buff);
	//			memset(rx_buff,0,256);
	//
	//			sprintf((char *)rx_buff,"Anolog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);
	//			uart0_send_string((char *)rx_buff);
	//			memset(rx_buff,0,256);
	//
	//			sprintf((char *)rx_buff,"Normalize %d-%d-%d-%d-%d-%d-%d-%d\r\n",Normal[0],Normal[1],Normal[2],Normal[3],Normal[4],Normal[5],Normal[6],Normal[7]);
	//			uart0_send_string((char *)rx_buff);
	//			memset(rx_buff,0,256);
	//			delay_ms(1);
	//		}
}

////���������ⲿ�ж�
// void GROUP1_IRQHandler(void)
//{
//     //��ȡGroup1���жϼĴ���������жϱ�־λ
//     switch( DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1) )
//     {
//         //����Ƿ��ǵ�GPIOB�˿��жϣ�ע����INT_IIDX
//         case Encoder_1_INT_IIDX:
//					if(DL_GPIO_readPins(Encoder_1_PORT, Encoder_1_E1A_PIN) == 1)

//        break;
//    }
//}
/****************************End*****************************/
