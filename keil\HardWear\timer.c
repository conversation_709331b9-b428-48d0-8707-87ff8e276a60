#include "timer.h"

volatile uint8_t led_flash_time = 0;

void timer0_init(void)
{
	// �����ʱ���жϱ�־
	NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
	// ʹ�ܶ�ʱ���ж�
	NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
}

void tim_tick_init(void)
{
	// �����ʱ���жϱ�־
	NVIC_ClearPendingIRQ(TIMER_TICK_INST_INT_IRQN);
	// ʹ�ܶ�ʱ���ж�
	NVIC_EnableIRQ(TIMER_TICK_INST_INT_IRQN);
}

// ��ʱ��G0���жϷ�������������Ϊ1ms������
void TIMG0_IRQHandler(void)
{
	static uint32_t timer_count = 1;
	// ��������˶�ʱ���ж�
	if (DL_TimerG_getPendingInterrupt(TIMER_0_INST) == DL_TIMER_IIDX_ZERO) // �����0����ж�
	{
		timer_count = timer_count < 1000 ? timer_count + 1 : 1;
		if (timer_count % 1000 == 0)
			led_flash_time = 1;
	}
}

// �߼���ʱ��A0��������Ϊ20ms������
void TIMA0_IRQHandler(void)
{
	if (DL_TimerA_getPendingInterrupt(TIMA0) == DL_TIMER_IIDX_ZERO)
	{
		// 每20ms更新一次编码器计数值
		left_encoder_update();
		right_encoder_update();
	}
}
