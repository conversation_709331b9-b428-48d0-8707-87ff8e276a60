#ifndef _HW_ENCODER_H_
#define _HW_ENCODER_H_

#include "ti_msp_dl_config.h"

typedef enum {
    FORWARD,  // ����
    REVERSAL  // ����
} ENCODER_DIR;

typedef struct {
    volatile long long temp_count; //����ʵʱ����ֵ
    int count;         						 //���ݶ�ʱ��ʱ����µļ���ֵ
    ENCODER_DIR dir;            	 //��ת����
} ENCODER_RES;

void GROUP1_IRQHandler(void);
void encoder_init();

void right_encoder_update(void);
ENCODER_DIR get_right_encoder_dir(void);
int get_right_encoder_count(void);

void left_encoder_update(void);
ENCODER_DIR get_left_encoder_dir(void);
int get_left_encoder_count(void);

#endif