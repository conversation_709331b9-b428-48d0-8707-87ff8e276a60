Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    empty.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text._sys_exit) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text._sys_exit) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text._sys_exit) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text._sys_exit) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text._sys_exit) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text._sys_exit) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text._sys_exit) refers to empty.o(.text._sys_exit) for [Anonymous Symbol]
    empty.o(.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to hw_encoder.o(.text.encoder_init) for encoder_init
    empty.o(.text.main) refers to hw_motor.o(.text.set_motor) for set_motor
    empty.o(.text.main) refers to hw_encoder.o(.text.get_right_encoder_count) for get_right_encoder_count
    empty.o(.text.main) refers to hw_encoder.o(.text.get_left_encoder_count) for get_left_encoder_count
    empty.o(.text.main) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for DL_Timer_getCaptureCompareValue
    empty.o(.text.main) refers to noretval__2printf.o(.text) for __2printf
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.text.UART0_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.UART0_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.UART0_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.UART0_IRQHandler) refers to usart.o(.text.uart0_send_char) for uart0_send_char
    empty.o(.text.UART0_IRQHandler) refers to empty.o(.bss.uart_data) for uart_data
    empty.o(.ARM.exidx.text.UART0_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.UART0_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.UART0_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.UART0_IRQHandler) refers to empty.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    empty.o(.text.TIMG0_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.TIMG0_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.TIMG0_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.data.TIMG0_IRQHandler.timer_count) for [Anonymous Symbol]
    empty.o(.text.TIMG0_IRQHandler) refers to empty.o(.bss.led_flash_time) for led_flash_time
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.TIMG0_IRQHandler) refers to empty.o(.text.TIMG0_IRQHandler) for [Anonymous Symbol]
    empty.o(.bss.led_flash_time) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.led_flash_time) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.led_flash_time) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.delay_times) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.delay_times) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.delay_times) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.uart_data) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.uart_data) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.uart_data) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.rx_buff) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.rx_buff) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.rx_buff) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.IIC_write_buff) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.IIC_write_buff) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.IIC_write_buff) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.Anolog) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.Anolog) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.Anolog) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.Normal) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.Normal) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.Normal) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.data.TIMG0_IRQHandler.timer_count) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.data.TIMG0_IRQHandler.timer_count) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.data.TIMG0_IRQHandler.timer_count) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.__stdout) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.__stdout) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.__stdout) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.Digtal) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.Digtal) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.Digtal) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to hw_encoder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.TIMG0_IRQHandler) for TIMG0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) for SYSCFG_DL_PWM_LED_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) for SYSCFG_DL_PWM_MOTOR_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_MOTORBackup) for gPWM_MOTORBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.rodata.gPWM_LEDConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to ti_msp_dl_config.o(.rodata.gPWM_MOTORClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to ti_msp_dl_config.o(.rodata.gPWM_MOTORConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_MOTOR_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_MOTORBackup) for gPWM_MOTORBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_MOTORBackup) for gPWM_MOTORBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.LED_flash) refers to led.o(.text.LED_flash) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.delay_ms) refers to led.o(.text.delay_ms) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.KEY_control_LED) refers to key.o(.text.KEY_control_LED) for [Anonymous Symbol]
    key.o(.text.Get_KEY) refers to led.o(.text.delay_ms) for delay_ms
    key.o(.ARM.exidx.text.Get_KEY) refers to key.o(.text.Get_KEY) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.uart0_send_char) refers to usart.o(.text.uart0_send_char) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.uart0_send_string) refers to usart.o(.text.uart0_send_string) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.fputc) refers to usart.o(.text.fputc) for [Anonymous Symbol]
    pwm.o(.text.PWM_LED) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    pwm.o(.text.PWM_LED) refers to led.o(.text.delay_ms) for delay_ms
    pwm.o(.ARM.exidx.text.PWM_LED) refers to pwm.o(.text.PWM_LED) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.ARM.exidx.text.hardware_IIC_WirteByte) refers to iic.o(.text.hardware_IIC_WirteByte) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_WirteBytes) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    iic.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.ARM.exidx.text.hardware_IIC_WirteBytes) refers to iic.o(.text.hardware_IIC_WirteBytes) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.ARM.exidx.text.hardware_IIC_ReadByte) refers to iic.o(.text.hardware_IIC_ReadByte) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.ARM.exidx.text.hardware_IIC_ReadBytes) refers to iic.o(.text.hardware_IIC_ReadBytes) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.delay_us_Boss) refers to software_iic.o(.text.delay_us_Boss) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.delay_ms_Boss) refers to software_iic.o(.text.delay_ms_Boss) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_Start) refers to software_iic.o(.text.IIC_Start) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_Stop) refers to software_iic.o(.text.IIC_Stop) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_WaitAck) refers to software_iic.o(.text.IIC_WaitAck) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_SendAck) refers to software_iic.o(.text.IIC_SendAck) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_SendNAck) refers to software_iic.o(.text.IIC_SendNAck) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_SendByte) refers to software_iic.o(.text.IIC_SendByte) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_RecvByte) refers to software_iic.o(.text.IIC_RecvByte) for [Anonymous Symbol]
    software_iic.o(.text.IIC_ReadByte) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.IIC_ReadByte) refers to software_iic.o(.text.IIC_ReadByte) for [Anonymous Symbol]
    software_iic.o(.text.IIC_ReadBytes) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.IIC_ReadBytes) refers to software_iic.o(.text.IIC_ReadBytes) for [Anonymous Symbol]
    software_iic.o(.text.IIC_WriteByte) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.IIC_WriteByte) refers to software_iic.o(.text.IIC_WriteByte) for [Anonymous Symbol]
    software_iic.o(.text.IIC_WriteBytes) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.IIC_WriteBytes) refers to software_iic.o(.text.IIC_WriteBytes) for [Anonymous Symbol]
    software_iic.o(.text.Ping) refers to software_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(.ARM.exidx.text.Ping) refers to software_iic.o(.text.Ping) for [Anonymous Symbol]
    hw_motor.o(.text.set_motor_B) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    hw_motor.o(.ARM.exidx.text.set_motor_B) refers to hw_motor.o(.text.set_motor_B) for [Anonymous Symbol]
    hw_motor.o(.text.set_motor_A) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    hw_motor.o(.ARM.exidx.text.set_motor_A) refers to hw_motor.o(.text.set_motor_A) for [Anonymous Symbol]
    hw_motor.o(.text.set_motor) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    hw_motor.o(.ARM.exidx.text.set_motor) refers to hw_motor.o(.text.set_motor) for [Anonymous Symbol]
    hw_motor.o(.text.stop_motor) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    hw_motor.o(.ARM.exidx.text.stop_motor) refers to hw_motor.o(.text.stop_motor) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.encoder_init) refers to hw_encoder.o(.text.encoder_init) for [Anonymous Symbol]
    hw_encoder.o(.text.get_left_encoder_count) refers to hw_encoder.o(.bss.Left_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_left_encoder_count) refers to hw_encoder.o(.text.get_left_encoder_count) for [Anonymous Symbol]
    hw_encoder.o(.text.get_left_encoder_dir) refers to hw_encoder.o(.bss.Left_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_left_encoder_dir) refers to hw_encoder.o(.text.get_left_encoder_dir) for [Anonymous Symbol]
    hw_encoder.o(.text.left_encoder_update) refers to hw_encoder.o(.bss.Left_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.left_encoder_update) refers to hw_encoder.o(.text.left_encoder_update) for [Anonymous Symbol]
    hw_encoder.o(.text.get_right_encoder_count) refers to hw_encoder.o(.bss.Right_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_right_encoder_count) refers to hw_encoder.o(.text.get_right_encoder_count) for [Anonymous Symbol]
    hw_encoder.o(.text.get_right_encoder_dir) refers to hw_encoder.o(.bss.Right_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_right_encoder_dir) refers to hw_encoder.o(.text.get_right_encoder_dir) for [Anonymous Symbol]
    hw_encoder.o(.text.right_encoder_update) refers to hw_encoder.o(.bss.Right_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.right_encoder_update) refers to hw_encoder.o(.text.right_encoder_update) for [Anonymous Symbol]
    hw_encoder.o(.text.GROUP1_IRQHandler) refers to hw_encoder.o(.bss.Left_encoder) for [Anonymous Symbol]
    hw_encoder.o(.text.GROUP1_IRQHandler) refers to hw_encoder.o(.bss.Right_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to hw_encoder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to empty.o(.bss.__stdout) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to empty.o(.bss.__stdout) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy.o(.text) refers to rt_memcpy.o(.emb_text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text.fputc) for fputc
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to empty.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to empty.o(.text._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to empty.o(.text._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to empty.o(.text._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text._sys_exit), (8 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.exidx.text.TIMG0_IRQHandler), (8 bytes).
    Removing empty.o(.bss.delay_times), (4 bytes).
    Removing empty.o(.bss.rx_buff), (256 bytes).
    Removing empty.o(.bss.IIC_write_buff), (10 bytes).
    Removing empty.o(.bss.Anolog), (8 bytes).
    Removing empty.o(.bss.Normal), (8 bytes).
    Removing empty.o(.bss.Digtal), (1 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_LED_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_MOTOR_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (40 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (48 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing led.o(.text), (0 bytes).
    Removing led.o(.text.LED_flash), (56 bytes).
    Removing led.o(.ARM.exidx.text.LED_flash), (8 bytes).
    Removing led.o(.text.delay_ms), (24 bytes).
    Removing led.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.text.KEY_control_LED), (32 bytes).
    Removing key.o(.ARM.exidx.text.KEY_control_LED), (8 bytes).
    Removing key.o(.text.Get_KEY), (64 bytes).
    Removing key.o(.ARM.exidx.text.Get_KEY), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing usart.o(.text.uart0_send_string), (56 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing usart.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing pwm.o(.text), (0 bytes).
    Removing pwm.o(.text.PWM_LED), (72 bytes).
    Removing pwm.o(.ARM.exidx.text.PWM_LED), (8 bytes).
    Removing iic.o(.text), (0 bytes).
    Removing iic.o(.text.hardware_IIC_WirteByte), (164 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_WirteByte), (8 bytes).
    Removing iic.o(.text.hardware_IIC_WirteBytes), (188 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_WirteBytes), (8 bytes).
    Removing iic.o(.text.hardware_IIC_ReadByte), (240 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_ReadByte), (8 bytes).
    Removing iic.o(.text.hardware_IIC_ReadBytes), (324 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_ReadBytes), (8 bytes).
    Removing software_iic.o(.text), (0 bytes).
    Removing software_iic.o(.text.delay_us_Boss), (68 bytes).
    Removing software_iic.o(.ARM.exidx.text.delay_us_Boss), (8 bytes).
    Removing software_iic.o(.text.delay_ms_Boss), (72 bytes).
    Removing software_iic.o(.ARM.exidx.text.delay_ms_Boss), (8 bytes).
    Removing software_iic.o(.text.IIC_Start), (144 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing software_iic.o(.text.IIC_Stop), (192 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing software_iic.o(.text.IIC_WaitAck), (156 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_WaitAck), (8 bytes).
    Removing software_iic.o(.text.IIC_SendAck), (84 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_SendAck), (8 bytes).
    Removing software_iic.o(.text.IIC_SendNAck), (84 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_SendNAck), (8 bytes).
    Removing software_iic.o(.text.IIC_SendByte), (452 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_SendByte), (8 bytes).
    Removing software_iic.o(.text.IIC_RecvByte), (356 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_RecvByte), (8 bytes).
    Removing software_iic.o(.text.IIC_ReadByte), (736 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_ReadByte), (8 bytes).
    Removing software_iic.o(.text.IIC_ReadBytes), (1528 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_ReadBytes), (8 bytes).
    Removing software_iic.o(.text.IIC_WriteByte), (900 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_WriteByte), (8 bytes).
    Removing software_iic.o(.text.IIC_WriteBytes), (928 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_WriteBytes), (8 bytes).
    Removing software_iic.o(.text.Ping), (30 bytes).
    Removing software_iic.o(.ARM.exidx.text.Ping), (8 bytes).
    Removing software_iic.o(.bss.delay_times_2), (4 bytes).
    Removing hw_motor.o(.text), (0 bytes).
    Removing hw_motor.o(.text.set_motor_B), (32 bytes).
    Removing hw_motor.o(.ARM.exidx.text.set_motor_B), (8 bytes).
    Removing hw_motor.o(.text.set_motor_A), (36 bytes).
    Removing hw_motor.o(.ARM.exidx.text.set_motor_A), (8 bytes).
    Removing hw_motor.o(.ARM.exidx.text.set_motor), (8 bytes).
    Removing hw_motor.o(.text.stop_motor), (60 bytes).
    Removing hw_motor.o(.ARM.exidx.text.stop_motor), (8 bytes).
    Removing hw_encoder.o(.text), (0 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.encoder_init), (8 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_left_encoder_count), (8 bytes).
    Removing hw_encoder.o(.text.get_left_encoder_dir), (12 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_left_encoder_dir), (8 bytes).
    Removing hw_encoder.o(.text.left_encoder_update), (24 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.left_encoder_update), (8 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_right_encoder_count), (8 bytes).
    Removing hw_encoder.o(.text.get_right_encoder_dir), (12 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_right_encoder_dir), (8 bytes).
    Removing hw_encoder.o(.text.right_encoder_update), (24 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.right_encoder_update), (8 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.text.DL_I2C_setClockConfig), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).

249 unused section(s) (total 12571 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    IIC.c                                    0x00000000   Number         0  iic.o ABSOLUTE
    KEY.c                                    0x00000000   Number         0  key.o ABSOLUTE
    LED.c                                    0x00000000   Number         0  led.o ABSOLUTE
    PWM.c                                    0x00000000   Number         0  pwm.o ABSOLUTE
    USART.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    hw_encoder.c                             0x00000000   Number         0  hw_encoder.o ABSOLUTE
    hw_motor.c                               0x00000000   Number         0  hw_motor.o ABSOLUTE
    software_iic.c                           0x00000000   Number         0  software_iic.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_copy                           0x00000120   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x00000140   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000148   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x00000164   Section        2  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x00000166   Section       10  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x00000170   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x00000174   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000176   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000178   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0000017a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0000017c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0000017c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0000017c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x00000182   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000182   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000186   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000186   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0000018e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x00000190   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x00000190   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x00000194   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0000019c   Section       48  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000001cc   Section        0  noretval__2printf.o(.text)
    .text                                    0x000001e8   Section        0  __printf.o(.text)
    .text                                    0x00000254   Section        0  _printf_dec.o(.text)
    .text                                    0x000002c0   Section        0  heapauxi.o(.text)
    .text                                    0x000002c6   Section        0  _printf_intcommon.o(.text)
    .text                                    0x00000378   Section        0  _printf_char_file.o(.text)
    .text                                    0x000003a0   Section        0  rtudiv10.o(.text)
    _printf_input_char                       0x000003c9   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x000003c8   Section        0  _printf_char_common.o(.text)
    .text                                    0x000003f8   Section        0  ferror.o(.text)
    .text                                    0x00000400   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x0000043e   Section        0  exit.o(.text)
    .text                                    0x00000450   Section        8  libspace.o(.text)
    [Anonymous Symbol]                       0x00000458   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x00000464   Section        0  dl_timer.o(.text.DL_Timer_getCaptureCompareValue)
    [Anonymous Symbol]                       0x00000470   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x00000564   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x00000568   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x0000056c   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x00000570   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x00000574   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    [Anonymous Symbol]                       0x00000578   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x00000658   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_1                             0x0000065c   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x00000660   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x00000664   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    [Anonymous Symbol]                       0x00000668   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x00000680   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00000684   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00000698   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x0000069c   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x000006a8   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x000006ac   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x000006c4   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x000006c8   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00000708   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x0000070c   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x00000710   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x00000724   Section        0  hw_encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.7_0                             0x000007a8   Number         4  hw_encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.7_1                             0x000007ac   Number         4  hw_encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.7_2                             0x000007b0   Number         4  hw_encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.7_3                             0x000007b4   Number         4  hw_encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.7_4                             0x000007b8   Number         4  hw_encoder.o(.text.GROUP1_IRQHandler)
    __arm_cp.7_5                             0x000007bc   Number         4  hw_encoder.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x000007c0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x0000085c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00000860   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00000864   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00000868   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x0000086c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00000870   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00000874   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x00000878   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_8                             0x0000087c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_9                             0x00000880   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_10                            0x00000884   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_11                            0x00000888   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_12                            0x0000088c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_13                            0x00000890   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00000894   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_0                             0x000008f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_1                             0x000008f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_2                             0x000008f8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_3                             0x000008fc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_5                             0x00000900   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    [Anonymous Symbol]                       0x00000904   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_1                             0x0000097c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_2                             0x00000980   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_3                             0x00000984   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_4                             0x00000988   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_5                             0x0000098c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    [Anonymous Symbol]                       0x00000990   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x000009c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x000009c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x000009cc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.8_0                             0x000009e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x000009e8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_0                             0x00000a0c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_1                             0x00000a10   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_2                             0x00000a14   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_3                             0x00000a18   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_4                             0x00000a1c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00000a20   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_0                             0x00000a84   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_1                             0x00000a88   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_2                             0x00000a8c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_3                             0x00000a90   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_4                             0x00000a94   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_5                             0x00000a98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.7_6                             0x00000a9c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00000aa0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00000ad0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00000ad4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00000ad8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00000b0c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00000b10   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00000b14   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00000b18   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00000b1c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00000b20   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00000b24   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x00000b28   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00000b2c   Section        0  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.3_0                             0x00000b60   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.3_1                             0x00000b64   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.3_2                             0x00000b68   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.3_3                             0x00000b6c   Number         4  empty.o(.text.TIMG0_IRQHandler)
    __arm_cp.3_4                             0x00000b70   Number         4  empty.o(.text.TIMG0_IRQHandler)
    [Anonymous Symbol]                       0x00000b74   Section        0  empty.o(.text.UART0_IRQHandler)
    __arm_cp.2_0                             0x00000b90   Number         4  empty.o(.text.UART0_IRQHandler)
    __arm_cp.2_1                             0x00000b94   Number         4  empty.o(.text.UART0_IRQHandler)
    __arm_cp.2_2                             0x00000b98   Number         4  empty.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x00000b9c   Section        0  empty.o(.text._sys_exit)
    [Anonymous Symbol]                       0x00000ba0   Section        0  hw_encoder.o(.text.encoder_init)
    [Anonymous Symbol]                       0x00000bb0   Section        0  usart.o(.text.fputc)
    [Anonymous Symbol]                       0x00000bd0   Section        0  hw_encoder.o(.text.get_left_encoder_count)
    __arm_cp.1_0                             0x00000bd8   Number         4  hw_encoder.o(.text.get_left_encoder_count)
    [Anonymous Symbol]                       0x00000bdc   Section        0  hw_encoder.o(.text.get_right_encoder_count)
    __arm_cp.4_0                             0x00000be4   Number         4  hw_encoder.o(.text.get_right_encoder_count)
    [Anonymous Symbol]                       0x00000be8   Section        0  empty.o(.text.main)
    __arm_cp.1_0                             0x00000c50   Number         4  empty.o(.text.main)
    __arm_cp.1_1                             0x00000c54   Number         4  empty.o(.text.main)
    __arm_cp.1_2                             0x00000c58   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x00000c8c   Section        0  hw_motor.o(.text.set_motor)
    __arm_cp.2_0                             0x00000cbc   Number         4  hw_motor.o(.text.set_motor)
    __arm_cp.2_1                             0x00000cc0   Number         4  hw_motor.o(.text.set_motor)
    [Anonymous Symbol]                       0x00000cc4   Section        0  usart.o(.text.uart0_send_char)
    __arm_cp.0_0                             0x00000ce4   Number         4  usart.o(.text.uart0_send_char)
    gPWM_LEDClockConfig                      0x00000ce8   Data           3  ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig)
    [Anonymous Symbol]                       0x00000ce8   Section        0  ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig)
    gPWM_LEDConfig                           0x00000cec   Data           8  ti_msp_dl_config.o(.rodata.gPWM_LEDConfig)
    [Anonymous Symbol]                       0x00000cec   Section        0  ti_msp_dl_config.o(.rodata.gPWM_LEDConfig)
    gPWM_MOTORClockConfig                    0x00000cf4   Data           3  ti_msp_dl_config.o(.rodata.gPWM_MOTORClockConfig)
    [Anonymous Symbol]                       0x00000cf4   Section        0  ti_msp_dl_config.o(.rodata.gPWM_MOTORClockConfig)
    gPWM_MOTORConfig                         0x00000cf8   Data           8  ti_msp_dl_config.o(.rodata.gPWM_MOTORConfig)
    [Anonymous Symbol]                       0x00000cf8   Section        0  ti_msp_dl_config.o(.rodata.gPWM_MOTORConfig)
    gTIMER_0ClockConfig                      0x00000d00   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x00000d00   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x00000d04   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x00000d04   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gUART_0ClockConfig                       0x00000d18   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00000d18   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x00000d1a   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00000d1a   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    TIMG0_IRQHandler.timer_count             0x20200000   Data           4  empty.o(.data.TIMG0_IRQHandler.timer_count)
    [Anonymous Symbol]                       0x20200000   Section        0  empty.o(.data.TIMG0_IRQHandler.timer_count)
    .bss                                     0x20200008   Section       96  libspace.o(.bss)
    Left_encoder                             0x20200068   Data          16  hw_encoder.o(.bss.Left_encoder)
    [Anonymous Symbol]                       0x20200068   Section        0  hw_encoder.o(.bss.Left_encoder)
    Right_encoder                            0x20200078   Data          16  hw_encoder.o(.bss.Right_encoder)
    [Anonymous Symbol]                       0x20200078   Section        0  hw_encoder.o(.bss.Right_encoder)
    Heap_Mem                                 0x20200220   Data         256  startup_mspm0g350x_uvision.o(HEAP)
    HEAP                                     0x20200220   Section      256  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x20200320   Data         256  startup_mspm0g350x_uvision.o(STACK)
    STACK                                    0x20200320   Section      256  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x20200420   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x00000121   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x00000141   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000149   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x00000165   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x00000167   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent_end                      0x00000171   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x00000175   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x00000179   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0000017d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0000017d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0000017d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x00000183   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x00000183   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000187   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000187   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0000018f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x00000191   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x00000191   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x00000195   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0000019d   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000001a1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000001a3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000001a5   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000001a7   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000001a9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000001ab   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x000001ad   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __2printf                                0x000001cd   Thumb Code    22  noretval__2printf.o(.text)
    __printf                                 0x000001e9   Thumb Code   108  __printf.o(.text)
    _printf_int_dec                          0x00000255   Thumb Code    90  _printf_dec.o(.text)
    __use_two_region_memory                  0x000002c1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x000002c3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x000002c5   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x000002c7   Thumb Code   176  _printf_intcommon.o(.text)
    _printf_char_file                        0x00000379   Thumb Code    34  _printf_char_file.o(.text)
    __rt_udiv10                              0x000003a1   Thumb Code    40  rtudiv10.o(.text)
    _printf_char_common                      0x000003d3   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x000003f9   Thumb Code     8  ferror.o(.text)
    __user_setup_stackheap                   0x00000401   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x0000043f   Thumb Code    16  exit.o(.text)
    __user_libspace                          0x00000451   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x00000451   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x00000451   Thumb Code     0  libspace.o(.text)
    DL_Common_delayCycles                    0x00000459   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_Timer_getCaptureCompareValue          0x00000465   Thumb Code    12  dl_timer.o(.text.DL_Timer_getCaptureCompareValue)
    DL_Timer_initFourCCPWMMode               0x00000471   Thumb Code   264  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_initTimerMode                   0x00000579   Thumb Code   240  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00000669   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00000685   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x0000069d   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x000006ad   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x000006c9   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00000711   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    GROUP1_IRQHandler                        0x00000725   Thumb Code   132  hw_encoder.o(.text.GROUP1_IRQHandler)
    SYSCFG_DL_GPIO_init                      0x000007c1   Thumb Code   156  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_PWM_LED_init                   0x00000895   Thumb Code    92  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    SYSCFG_DL_PWM_MOTOR_init                 0x00000905   Thumb Code   120  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    SYSCFG_DL_SYSCTL_init                    0x00000991   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x000009cd   Thumb Code    24  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x000009e9   Thumb Code    36  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_UART_0_init                    0x00000a21   Thumb Code   100  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x00000aa1   Thumb Code    48  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00000ad9   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    TIMG0_IRQHandler                         0x00000b2d   Thumb Code    52  empty.o(.text.TIMG0_IRQHandler)
    UART0_IRQHandler                         0x00000b75   Thumb Code    28  empty.o(.text.UART0_IRQHandler)
    _sys_exit                                0x00000b9d   Thumb Code     2  empty.o(.text._sys_exit)
    encoder_init                             0x00000ba1   Thumb Code    16  hw_encoder.o(.text.encoder_init)
    fputc                                    0x00000bb1   Thumb Code    32  usart.o(.text.fputc)
    get_left_encoder_count                   0x00000bd1   Thumb Code     8  hw_encoder.o(.text.get_left_encoder_count)
    get_right_encoder_count                  0x00000bdd   Thumb Code     8  hw_encoder.o(.text.get_right_encoder_count)
    main                                     0x00000be9   Thumb Code   104  empty.o(.text.main)
    set_motor                                0x00000c8d   Thumb Code    48  hw_motor.o(.text.set_motor)
    uart0_send_char                          0x00000cc5   Thumb Code    32  usart.o(.text.uart0_send_char)
    Region$$Table$$Base                      0x00000d28   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00000d48   Number         0  anon$$obj.o(Region$$Table)
    __libspace_start                         0x20200008   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20200068   Data           0  libspace.o(.bss)
    __stdout                                 0x20200088   Data          84  empty.o(.bss.__stdout)
    gPWM_LEDBackup                           0x202000dc   Data         160  ti_msp_dl_config.o(.bss.gPWM_LEDBackup)
    gPWM_MOTORBackup                         0x2020017c   Data         160  ti_msp_dl_config.o(.bss.gPWM_MOTORBackup)
    led_flash_time                           0x2020021c   Data           1  empty.o(.bss.led_flash_time)
    uart_data                                0x2020021d   Data           1  empty.o(.bss.uart_data)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00000d50, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00000d48, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           29    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO          446  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO          634    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x0000001a   Code   RO          638    !!handler_copy      c_p.l(__scatter_copy.o)
    0x0000013a   0x0000013a   0x00000006   PAD
    0x00000140   0x00000140   0x00000002   Code   RO          635    !!handler_null      c_p.l(__scatter.o)
    0x00000142   0x00000142   0x00000006   PAD
    0x00000148   0x00000148   0x0000001c   Code   RO          640    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000164   0x00000164   0x00000002   Code   RO          431    .ARM.Collect$$_printf_percent$$00000000  c_p.l(_printf_percent.o)
    0x00000166   0x00000166   0x0000000a   Code   RO          430    .ARM.Collect$$_printf_percent$$00000009  c_p.l(_printf_d.o)
    0x00000170   0x00000170   0x00000004   Code   RO          457    .ARM.Collect$$_printf_percent$$00000017  c_p.l(_printf_percent_end.o)
    0x00000174   0x00000174   0x00000002   Code   RO          499    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000176   0x00000176   0x00000000   Code   RO          513    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          515    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          517    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          520    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          522    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          524    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          527    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          529    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          531    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          533    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          535    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          537    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          539    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          541    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          543    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          545    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          547    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          551    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          553    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          555    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          557    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000002   Code   RO          558    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000178   0x00000178   0x00000002   Code   RO          586    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          617    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          619    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          622    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          625    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          627    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          630    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000002   Code   RO          631    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x0000017c   0x0000017c   0x00000000   Code   RO          448    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x0000017c   0x0000017c   0x00000000   Code   RO          461    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x0000017c   0x0000017c   0x00000006   Code   RO          473    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x00000182   0x00000182   0x00000000   Code   RO          463    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x00000182   0x00000182   0x00000004   Code   RO          464    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000186   0x00000186   0x00000000   Code   RO          466    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000186   0x00000186   0x00000008   Code   RO          467    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0000018e   0x0000018e   0x00000002   Code   RO          504    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x00000190   0x00000190   0x00000000   Code   RO          560    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x00000190   0x00000190   0x00000004   Code   RO          561    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x00000194   0x00000194   0x00000006   Code   RO          562    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0000019a   0x0000019a   0x00000002   PAD
    0x0000019c   0x0000019c   0x00000030   Code   RO           30    .text               startup_mspm0g350x_uvision.o
    0x000001cc   0x000001cc   0x0000001c   Code   RO          404    .text               c_p.l(noretval__2printf.o)
    0x000001e8   0x000001e8   0x0000006c   Code   RO          406    .text               c_p.l(__printf.o)
    0x00000254   0x00000254   0x0000006c   Code   RO          408    .text               c_p.l(_printf_dec.o)
    0x000002c0   0x000002c0   0x00000006   Code   RO          444    .text               c_p.l(heapauxi.o)
    0x000002c6   0x000002c6   0x000000b0   Code   RO          453    .text               c_p.l(_printf_intcommon.o)
    0x00000376   0x00000376   0x00000002   PAD
    0x00000378   0x00000378   0x00000028   Code   RO          455    .text               c_p.l(_printf_char_file.o)
    0x000003a0   0x000003a0   0x00000028   Code   RO          458    .text               c_p.l(rtudiv10.o)
    0x000003c8   0x000003c8   0x00000030   Code   RO          477    .text               c_p.l(_printf_char_common.o)
    0x000003f8   0x000003f8   0x00000008   Code   RO          479    .text               c_p.l(ferror.o)
    0x00000400   0x00000400   0x0000003e   Code   RO          483    .text               c_p.l(sys_stackheap_outer.o)
    0x0000043e   0x0000043e   0x00000010   Code   RO          488    .text               c_p.l(exit.o)
    0x0000044e   0x0000044e   0x00000002   PAD
    0x00000450   0x00000450   0x00000008   Code   RO          500    .text               c_p.l(libspace.o)
    0x00000458   0x00000458   0x0000000a   Code   RO          219    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x00000462   0x00000462   0x00000002   PAD
    0x00000464   0x00000464   0x0000000c   Code   RO          282    .text.DL_Timer_getCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00000470   0x00000470   0x00000108   Code   RO          340    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x00000578   0x00000578   0x000000f0   Code   RO          264    .text.DL_Timer_initTimerMode  driverlib.a(dl_timer.o)
    0x00000668   0x00000668   0x0000001c   Code   RO          306    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x00000684   0x00000684   0x00000018   Code   RO          314    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x0000069c   0x0000069c   0x00000010   Code   RO          266    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x000006ac   0x000006ac   0x0000001c   Code   RO          260    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x000006c8   0x000006c8   0x00000048   Code   RO          361    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00000710   0x00000710   0x00000012   Code   RO          363    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x00000722   0x00000722   0x00000002   PAD
    0x00000724   0x00000724   0x0000009c   Code   RO          207    .text.GROUP1_IRQHandler  hw_encoder.o
    0x000007c0   0x000007c0   0x000000d4   Code   RO           41    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00000894   0x00000894   0x00000070   Code   RO           45    .text.SYSCFG_DL_PWM_LED_init  ti_msp_dl_config.o
    0x00000904   0x00000904   0x0000008c   Code   RO           47    .text.SYSCFG_DL_PWM_MOTOR_init  ti_msp_dl_config.o
    0x00000990   0x00000990   0x0000003c   Code   RO           43    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x000009cc   0x000009cc   0x0000001c   Code   RO           53    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x000009e8   0x000009e8   0x00000038   Code   RO           49    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00000a20   0x00000a20   0x00000080   Code   RO           51    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00000aa0   0x00000aa0   0x00000038   Code   RO           37    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00000ad8   0x00000ad8   0x00000054   Code   RO           39    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00000b2c   0x00000b2c   0x00000048   Code   RO            8    .text.TIMG0_IRQHandler  empty.o
    0x00000b74   0x00000b74   0x00000028   Code   RO            6    .text.UART0_IRQHandler  empty.o
    0x00000b9c   0x00000b9c   0x00000002   Code   RO            2    .text._sys_exit     empty.o
    0x00000b9e   0x00000b9e   0x00000002   PAD
    0x00000ba0   0x00000ba0   0x00000010   Code   RO          193    .text.encoder_init  hw_encoder.o
    0x00000bb0   0x00000bb0   0x00000020   Code   RO          105    .text.fputc         usart.o
    0x00000bd0   0x00000bd0   0x0000000c   Code   RO          195    .text.get_left_encoder_count  hw_encoder.o
    0x00000bdc   0x00000bdc   0x0000000c   Code   RO          201    .text.get_right_encoder_count  hw_encoder.o
    0x00000be8   0x00000be8   0x000000a4   Code   RO            4    .text.main          empty.o
    0x00000c8c   0x00000c8c   0x00000038   Code   RO          181    .text.set_motor     hw_motor.o
    0x00000cc4   0x00000cc4   0x00000024   Code   RO          101    .text.uart0_send_char  usart.o
    0x00000ce8   0x00000ce8   0x00000003   Data   RO           61    .rodata.gPWM_LEDClockConfig  ti_msp_dl_config.o
    0x00000ceb   0x00000ceb   0x00000001   PAD
    0x00000cec   0x00000cec   0x00000008   Data   RO           62    .rodata.gPWM_LEDConfig  ti_msp_dl_config.o
    0x00000cf4   0x00000cf4   0x00000003   Data   RO           63    .rodata.gPWM_MOTORClockConfig  ti_msp_dl_config.o
    0x00000cf7   0x00000cf7   0x00000001   PAD
    0x00000cf8   0x00000cf8   0x00000008   Data   RO           64    .rodata.gPWM_MOTORConfig  ti_msp_dl_config.o
    0x00000d00   0x00000d00   0x00000003   Data   RO           65    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x00000d03   0x00000d03   0x00000001   PAD
    0x00000d04   0x00000d04   0x00000014   Data   RO           66    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x00000d18   0x00000d18   0x00000002   Data   RO           67    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x00000d1a   0x00000d1a   0x0000000a   Data   RO           68    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x00000d24   0x00000d24   0x00000004   PAD
    0x00000d28   0x00000d28   0x00000020   Data   RO          633    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00000d48, Size: 0x00000420, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00000d48   0x00000004   Data   RW           17    .data.TIMG0_IRQHandler.timer_count  empty.o
    0x20200004   0x00000d4c   0x00000004   PAD
    0x20200008        -       0x00000060   Zero   RW          501    .bss                c_p.l(libspace.o)
    0x20200068        -       0x00000010   Zero   RW          209    .bss.Left_encoder   hw_encoder.o
    0x20200078        -       0x00000010   Zero   RW          210    .bss.Right_encoder  hw_encoder.o
    0x20200088        -       0x00000054   Zero   RW           18    .bss.__stdout       empty.o
    0x202000dc        -       0x000000a0   Zero   RW           59    .bss.gPWM_LEDBackup  ti_msp_dl_config.o
    0x2020017c        -       0x000000a0   Zero   RW           60    .bss.gPWM_MOTORBackup  ti_msp_dl_config.o
    0x2020021c        -       0x00000001   Zero   RW           10    .bss.led_flash_time  empty.o
    0x2020021d        -       0x00000001   Zero   RW           12    .bss.uart_data      empty.o
    0x2020021e   0x00000d4c   0x00000002   PAD
    0x20200220        -       0x00000100   Zero   RW           28    HEAP                startup_mspm0g350x_uvision.o
    0x20200320        -       0x00000100   Zero   RW           27    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x000000ff, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       278         92          0          4         86       8664   empty.o
       196         32          0          0         32       6483   hw_encoder.o
        56          8          0          0          0       6701   hw_motor.o
        48         22        192          0        512        680   startup_mspm0g350x_uvision.o
       876        196         57          0        320      23991   ti_msp_dl_config.o
        68          4          0          0          0       3966   usart.o

    ----------------------------------------------------------------------
      1524        <USER>        <GROUP>          4        956      50485   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          7          0          6          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       108          0          0          0          0         76   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         88   _printf_char_common.o
        40          6          0          0          0         72   _printf_char_file.o
        10          0          0          0          0          0   _printf_d.o
       108         18          0          0          0         76   _printf_dec.o
       176          0          0          0          0         84   _printf_intcommon.o
         2          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        16          0          0          0          0         68   exit.o
         8          0          0          0          0         60   ferror.o
         6          0          0          0          0        136   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        28          6          0          0          0         84   noretval__2printf.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        40          0          0          0          0         60   rtudiv10.o
        62          0          0          0          0         80   sys_stackheap_outer.o
        10          0          0          0          0        803   dl_common.o
       612        192          0          0          0      41557   dl_timer.o
        90          8          0          0          0      14163   dl_uart.o

    ----------------------------------------------------------------------
      1588        <USER>          <GROUP>          0         96      57543   Library Totals
        26          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       850         50          0          0         96       1020   c_p.l
       712        200          0          0          0      56523   driverlib.a

    ----------------------------------------------------------------------
      1588        <USER>          <GROUP>          0         96      57543   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      3112        608        288          4       1052     107608   Grand Totals
      3112        608        288          4       1052     107608   ELF Image Totals
      3112        608        288          4          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 3400 (   3.32kB)
    Total RW  Size (RW Data + ZI Data)              1056 (   1.03kB)
    Total ROM Size (Code + RO Data + RW Data)       3404 (   3.32kB)

==============================================================================

