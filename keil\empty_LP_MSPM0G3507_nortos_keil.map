Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    empty.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text._sys_exit) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text._sys_exit) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text._sys_exit) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.text._sys_exit) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text._sys_exit) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text._sys_exit) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text._sys_exit) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.ARM.exidx.text._sys_exit) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text._sys_exit) refers to empty.o(.text._sys_exit) for [Anonymous Symbol]
    empty.o(.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.main) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    empty.o(.text.main) refers to timer.o(.text.timer0_init) for timer0_init
    empty.o(.text.main) refers to timer.o(.text.tim_tick_init) for tim_tick_init
    empty.o(.text.main) refers to usart.o(.text.Uart_init) for Uart_init
    empty.o(.text.main) refers to puts.o(.text) for puts
    empty.o(.text.main) refers to hw_encoder.o(.text.encoder_init) for encoder_init
    empty.o(.text.main) refers to hw_encoder.o(.text.reset_encoders) for reset_encoders
    empty.o(.text.main) refers to hw_encoder.o(.text.get_left_interrupt_count) for get_left_interrupt_count
    empty.o(.text.main) refers to noretval__2printf.o(.text) for __2printf
    empty.o(.text.main) refers to hw_motor.o(.text.set_motor) for set_motor
    empty.o(.text.main) refers to hw_encoder.o(.text.get_left_encoder_count) for get_left_encoder_count
    empty.o(.text.main) refers to hw_encoder.o(.text.get_left_interrupt_overflow) for get_left_interrupt_overflow
    empty.o(.text.main) refers to empty.o(.rodata.str1.1) for [Anonymous Symbol]
    empty.o(.text.main) refers to empty.o(.bss.main.loop_count) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.bss.delay_times) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.delay_times) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.delay_times) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.bss.delay_times) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.rx_buff) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.rx_buff) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.rx_buff) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.bss.rx_buff) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.IIC_write_buff) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.IIC_write_buff) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.IIC_write_buff) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.bss.IIC_write_buff) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.Anolog) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.Anolog) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.Anolog) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.bss.Anolog) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.Normal) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.Normal) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.Normal) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.bss.Normal) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.rodata.str1.1) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.main.loop_count) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.main.loop_count) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.main.loop_count) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.bss.main.loop_count) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.__stdout) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.__stdout) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.__stdout) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.bss.__stdout) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.Digtal) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.Digtal) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.Digtal) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.bss.Digtal) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to usart.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to timer.o(.text.TIMG0_IRQHandler) for TIMG0_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to timer.o(.text.TIMA0_IRQHandler) for TIMA0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) for SYSCFG_DL_PWM_LED_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) for SYSCFG_DL_PWM_MOTOR_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) for SYSCFG_DL_TIMER_TICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_MOTORBackup) for gPWM_MOTORBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.rodata.gPWM_LEDConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_LED_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to ti_msp_dl_config.o(.rodata.gPWM_MOTORClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) refers to ti_msp_dl_config.o(.rodata.gPWM_MOTORConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_MOTOR_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_TICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_MOTORBackup) for gPWM_MOTORBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_LEDBackup) for gPWM_LEDBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gPWM_MOTORBackup) for gPWM_MOTORBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_TICKBackup) for gTIMER_TICKBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.LED_flash) refers to led.o(.text.LED_flash) for [Anonymous Symbol]
    led.o(.ARM.exidx.text.delay_ms) refers to led.o(.text.delay_ms) for [Anonymous Symbol]
    key.o(.ARM.exidx.text.KEY_control_LED) refers to key.o(.text.KEY_control_LED) for [Anonymous Symbol]
    key.o(.text.Get_KEY) refers to led.o(.text.delay_ms) for delay_ms
    key.o(.ARM.exidx.text.Get_KEY) refers to key.o(.text.Get_KEY) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.uart0_send_char) refers to usart.o(.text.uart0_send_char) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.uart0_send_string) refers to usart.o(.text.uart0_send_string) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.fputc) refers to usart.o(.text.fputc) for [Anonymous Symbol]
    usart.o(.ARM.exidx.text.Uart_init) refers to usart.o(.text.Uart_init) for [Anonymous Symbol]
    usart.o(.text.UART0_IRQHandler) refers to usart.o(.bss.uart_data) for uart_data
    usart.o(.ARM.exidx.text.UART0_IRQHandler) refers to usart.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    pwm.o(.text.PWM_LED) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    pwm.o(.text.PWM_LED) refers to led.o(.text.delay_ms) for delay_ms
    pwm.o(.ARM.exidx.text.PWM_LED) refers to pwm.o(.text.PWM_LED) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.ARM.exidx.text.hardware_IIC_WirteByte) refers to iic.o(.text.hardware_IIC_WirteByte) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_WirteBytes) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    iic.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.ARM.exidx.text.hardware_IIC_WirteBytes) refers to iic.o(.text.hardware_IIC_WirteBytes) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.ARM.exidx.text.hardware_IIC_ReadByte) refers to iic.o(.text.hardware_IIC_ReadByte) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.ARM.exidx.text.hardware_IIC_ReadBytes) refers to iic.o(.text.hardware_IIC_ReadBytes) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.delay_us_Boss) refers to software_iic.o(.text.delay_us_Boss) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.delay_ms_Boss) refers to software_iic.o(.text.delay_ms_Boss) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_Start) refers to software_iic.o(.text.IIC_Start) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_Stop) refers to software_iic.o(.text.IIC_Stop) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_WaitAck) refers to software_iic.o(.text.IIC_WaitAck) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_SendAck) refers to software_iic.o(.text.IIC_SendAck) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_SendNAck) refers to software_iic.o(.text.IIC_SendNAck) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_SendByte) refers to software_iic.o(.text.IIC_SendByte) for [Anonymous Symbol]
    software_iic.o(.ARM.exidx.text.IIC_RecvByte) refers to software_iic.o(.text.IIC_RecvByte) for [Anonymous Symbol]
    software_iic.o(.text.IIC_ReadByte) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.IIC_ReadByte) refers to software_iic.o(.text.IIC_ReadByte) for [Anonymous Symbol]
    software_iic.o(.text.IIC_ReadBytes) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.IIC_ReadBytes) refers to software_iic.o(.text.IIC_ReadBytes) for [Anonymous Symbol]
    software_iic.o(.text.IIC_WriteByte) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.IIC_WriteByte) refers to software_iic.o(.text.IIC_WriteByte) for [Anonymous Symbol]
    software_iic.o(.text.IIC_WriteBytes) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.IIC_WriteBytes) refers to software_iic.o(.text.IIC_WriteBytes) for [Anonymous Symbol]
    software_iic.o(.text.Ping) refers to software_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(.ARM.exidx.text.Ping) refers to software_iic.o(.text.Ping) for [Anonymous Symbol]
    hw_motor.o(.text.set_motor_B) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    hw_motor.o(.ARM.exidx.text.set_motor_B) refers to hw_motor.o(.text.set_motor_B) for [Anonymous Symbol]
    hw_motor.o(.text.set_motor_A) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    hw_motor.o(.ARM.exidx.text.set_motor_A) refers to hw_motor.o(.text.set_motor_A) for [Anonymous Symbol]
    hw_motor.o(.text.set_motor) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    hw_motor.o(.ARM.exidx.text.set_motor) refers to hw_motor.o(.text.set_motor) for [Anonymous Symbol]
    hw_motor.o(.text.stop_motor) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    hw_motor.o(.ARM.exidx.text.stop_motor) refers to hw_motor.o(.text.stop_motor) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.encoder_init) refers to hw_encoder.o(.text.encoder_init) for [Anonymous Symbol]
    hw_encoder.o(.text.get_left_encoder_count) refers to hw_encoder.o(.bss.Left_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_left_encoder_count) refers to hw_encoder.o(.text.get_left_encoder_count) for [Anonymous Symbol]
    hw_encoder.o(.text.get_left_encoder_dir) refers to hw_encoder.o(.bss.Left_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_left_encoder_dir) refers to hw_encoder.o(.text.get_left_encoder_dir) for [Anonymous Symbol]
    hw_encoder.o(.text.left_encoder_update) refers to hw_encoder.o(.bss.Left_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.left_encoder_update) refers to hw_encoder.o(.text.left_encoder_update) for [Anonymous Symbol]
    hw_encoder.o(.text.get_right_encoder_count) refers to hw_encoder.o(.bss.Right_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_right_encoder_count) refers to hw_encoder.o(.text.get_right_encoder_count) for [Anonymous Symbol]
    hw_encoder.o(.text.get_right_encoder_dir) refers to hw_encoder.o(.bss.Right_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_right_encoder_dir) refers to hw_encoder.o(.text.get_right_encoder_dir) for [Anonymous Symbol]
    hw_encoder.o(.text.right_encoder_update) refers to hw_encoder.o(.bss.Right_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.right_encoder_update) refers to hw_encoder.o(.text.right_encoder_update) for [Anonymous Symbol]
    hw_encoder.o(.text.get_left_encoder_temp_count) refers to hw_encoder.o(.bss.Left_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_left_encoder_temp_count) refers to hw_encoder.o(.text.get_left_encoder_temp_count) for [Anonymous Symbol]
    hw_encoder.o(.text.get_right_encoder_temp_count) refers to hw_encoder.o(.bss.Right_encoder) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_right_encoder_temp_count) refers to hw_encoder.o(.text.get_right_encoder_temp_count) for [Anonymous Symbol]
    hw_encoder.o(.text.reset_encoders) refers to hw_encoder.o(.bss.Left_encoder) for [Anonymous Symbol]
    hw_encoder.o(.text.reset_encoders) refers to hw_encoder.o(.bss.Right_encoder) for [Anonymous Symbol]
    hw_encoder.o(.text.reset_encoders) refers to hw_encoder.o(.bss.left_interrupt_count) for [Anonymous Symbol]
    hw_encoder.o(.text.reset_encoders) refers to hw_encoder.o(.bss.right_interrupt_count) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.reset_encoders) refers to hw_encoder.o(.text.reset_encoders) for [Anonymous Symbol]
    hw_encoder.o(.text.get_left_interrupt_count) refers to hw_encoder.o(.bss.left_interrupt_count) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_left_interrupt_count) refers to hw_encoder.o(.text.get_left_interrupt_count) for [Anonymous Symbol]
    hw_encoder.o(.text.get_right_interrupt_count) refers to hw_encoder.o(.bss.right_interrupt_count) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_right_interrupt_count) refers to hw_encoder.o(.text.get_right_interrupt_count) for [Anonymous Symbol]
    hw_encoder.o(.text.get_left_interrupt_overflow) refers to hw_encoder.o(.bss.left_interrupt_overflow) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_left_interrupt_overflow) refers to hw_encoder.o(.text.get_left_interrupt_overflow) for [Anonymous Symbol]
    hw_encoder.o(.text.get_right_interrupt_overflow) refers to hw_encoder.o(.bss.right_interrupt_overflow) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.get_right_interrupt_overflow) refers to hw_encoder.o(.text.get_right_interrupt_overflow) for [Anonymous Symbol]
    hw_encoder.o(.text.GPIOA_IRQHandler) refers to hw_encoder.o(.bss.left_interrupt_count) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.GPIOA_IRQHandler) refers to hw_encoder.o(.text.GPIOA_IRQHandler) for [Anonymous Symbol]
    hw_encoder.o(.text.GPIOB_IRQHandler) refers to hw_encoder.o(.bss.right_interrupt_count) for [Anonymous Symbol]
    hw_encoder.o(.text.GPIOB_IRQHandler) refers to hw_encoder.o(.bss.Right_encoder) for [Anonymous Symbol]
    hw_encoder.o(.text.GPIOB_IRQHandler) refers to hw_encoder.o(.bss.right_interrupt_overflow) for [Anonymous Symbol]
    hw_encoder.o(.ARM.exidx.text.GPIOB_IRQHandler) refers to hw_encoder.o(.text.GPIOB_IRQHandler) for [Anonymous Symbol]
    timer.o(.ARM.exidx.text.timer0_init) refers to timer.o(.text.timer0_init) for [Anonymous Symbol]
    timer.o(.ARM.exidx.text.tim_tick_init) refers to timer.o(.text.tim_tick_init) for [Anonymous Symbol]
    timer.o(.text.TIMG0_IRQHandler) refers to timer.o(.data.TIMG0_IRQHandler.timer_count) for [Anonymous Symbol]
    timer.o(.text.TIMG0_IRQHandler) refers to timer.o(.bss.led_flash_time) for led_flash_time
    timer.o(.ARM.exidx.text.TIMG0_IRQHandler) refers to timer.o(.text.TIMG0_IRQHandler) for [Anonymous Symbol]
    timer.o(.text.TIMA0_IRQHandler) refers to hw_encoder.o(.text.left_encoder_update) for left_encoder_update
    timer.o(.text.TIMA0_IRQHandler) refers to hw_encoder.o(.text.right_encoder_update) for right_encoder_update
    timer.o(.ARM.exidx.text.TIMA0_IRQHandler) refers to timer.o(.text.TIMA0_IRQHandler) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to empty.o(.bss.__stdout) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to empty.o(.bss.__stdout) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    puts.o(.text) refers to usart.o(.text.fputc) for fputc
    puts.o(.text) refers to empty.o(.bss.__stdout) for __stdout
    rt_memcpy.o(.text) refers to rt_memcpy.o(.emb_text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text.fputc) for fputc
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to empty.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    _printf_char_common.o(.text) refers to __printf_ss.o(.text) for __printf
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to empty.o(.text._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to empty.o(.text._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to empty.o(.text._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text._sys_exit), (8 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.bss.delay_times), (4 bytes).
    Removing empty.o(.bss.rx_buff), (256 bytes).
    Removing empty.o(.bss.IIC_write_buff), (10 bytes).
    Removing empty.o(.bss.Anolog), (8 bytes).
    Removing empty.o(.bss.Normal), (8 bytes).
    Removing empty.o(.bss.Digtal), (1 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_LED_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_PWM_MOTOR_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_TICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (60 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (68 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing led.o(.text), (0 bytes).
    Removing led.o(.text.LED_flash), (56 bytes).
    Removing led.o(.ARM.exidx.text.LED_flash), (8 bytes).
    Removing led.o(.text.delay_ms), (24 bytes).
    Removing led.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing key.o(.text), (0 bytes).
    Removing key.o(.text.KEY_control_LED), (32 bytes).
    Removing key.o(.ARM.exidx.text.KEY_control_LED), (8 bytes).
    Removing key.o(.text.Get_KEY), (64 bytes).
    Removing key.o(.ARM.exidx.text.Get_KEY), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.text.uart0_send_char), (36 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing usart.o(.text.uart0_send_string), (56 bytes).
    Removing usart.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing usart.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing usart.o(.ARM.exidx.text.Uart_init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing pwm.o(.text), (0 bytes).
    Removing pwm.o(.text.PWM_LED), (72 bytes).
    Removing pwm.o(.ARM.exidx.text.PWM_LED), (8 bytes).
    Removing iic.o(.text), (0 bytes).
    Removing iic.o(.text.hardware_IIC_WirteByte), (164 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_WirteByte), (8 bytes).
    Removing iic.o(.text.hardware_IIC_WirteBytes), (188 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_WirteBytes), (8 bytes).
    Removing iic.o(.text.hardware_IIC_ReadByte), (240 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_ReadByte), (8 bytes).
    Removing iic.o(.text.hardware_IIC_ReadBytes), (324 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_ReadBytes), (8 bytes).
    Removing software_iic.o(.text), (0 bytes).
    Removing software_iic.o(.text.delay_us_Boss), (68 bytes).
    Removing software_iic.o(.ARM.exidx.text.delay_us_Boss), (8 bytes).
    Removing software_iic.o(.text.delay_ms_Boss), (72 bytes).
    Removing software_iic.o(.ARM.exidx.text.delay_ms_Boss), (8 bytes).
    Removing software_iic.o(.text.IIC_Start), (144 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing software_iic.o(.text.IIC_Stop), (192 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing software_iic.o(.text.IIC_WaitAck), (156 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_WaitAck), (8 bytes).
    Removing software_iic.o(.text.IIC_SendAck), (84 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_SendAck), (8 bytes).
    Removing software_iic.o(.text.IIC_SendNAck), (84 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_SendNAck), (8 bytes).
    Removing software_iic.o(.text.IIC_SendByte), (452 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_SendByte), (8 bytes).
    Removing software_iic.o(.text.IIC_RecvByte), (356 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_RecvByte), (8 bytes).
    Removing software_iic.o(.text.IIC_ReadByte), (736 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_ReadByte), (8 bytes).
    Removing software_iic.o(.text.IIC_ReadBytes), (1528 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_ReadBytes), (8 bytes).
    Removing software_iic.o(.text.IIC_WriteByte), (900 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_WriteByte), (8 bytes).
    Removing software_iic.o(.text.IIC_WriteBytes), (928 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_WriteBytes), (8 bytes).
    Removing software_iic.o(.text.Ping), (30 bytes).
    Removing software_iic.o(.ARM.exidx.text.Ping), (8 bytes).
    Removing software_iic.o(.bss.delay_times_2), (4 bytes).
    Removing hw_motor.o(.text), (0 bytes).
    Removing hw_motor.o(.text.set_motor_B), (32 bytes).
    Removing hw_motor.o(.ARM.exidx.text.set_motor_B), (8 bytes).
    Removing hw_motor.o(.text.set_motor_A), (36 bytes).
    Removing hw_motor.o(.ARM.exidx.text.set_motor_A), (8 bytes).
    Removing hw_motor.o(.ARM.exidx.text.set_motor), (8 bytes).
    Removing hw_motor.o(.text.stop_motor), (64 bytes).
    Removing hw_motor.o(.ARM.exidx.text.stop_motor), (8 bytes).
    Removing hw_encoder.o(.text), (0 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.encoder_init), (8 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_left_encoder_count), (8 bytes).
    Removing hw_encoder.o(.text.get_left_encoder_dir), (12 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_left_encoder_dir), (8 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.left_encoder_update), (8 bytes).
    Removing hw_encoder.o(.text.get_right_encoder_count), (12 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_right_encoder_count), (8 bytes).
    Removing hw_encoder.o(.text.get_right_encoder_dir), (12 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_right_encoder_dir), (8 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.right_encoder_update), (8 bytes).
    Removing hw_encoder.o(.text.get_left_encoder_temp_count), (12 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_left_encoder_temp_count), (8 bytes).
    Removing hw_encoder.o(.text.get_right_encoder_temp_count), (12 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_right_encoder_temp_count), (8 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.reset_encoders), (8 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_left_interrupt_count), (8 bytes).
    Removing hw_encoder.o(.text.get_right_interrupt_count), (12 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_right_interrupt_count), (8 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_left_interrupt_overflow), (8 bytes).
    Removing hw_encoder.o(.text.get_right_interrupt_overflow), (12 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.get_right_interrupt_overflow), (8 bytes).
    Removing hw_encoder.o(.text.GPIOA_IRQHandler), (28 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.GPIOA_IRQHandler), (8 bytes).
    Removing hw_encoder.o(.text.GPIOB_IRQHandler), (116 bytes).
    Removing hw_encoder.o(.ARM.exidx.text.GPIOB_IRQHandler), (8 bytes).
    Removing hw_encoder.o(.bss.right_interrupt_overflow), (4 bytes).
    Removing timer.o(.text), (0 bytes).
    Removing timer.o(.ARM.exidx.text.timer0_init), (8 bytes).
    Removing timer.o(.ARM.exidx.text.tim_tick_init), (8 bytes).
    Removing timer.o(.ARM.exidx.text.TIMG0_IRQHandler), (8 bytes).
    Removing timer.o(.ARM.exidx.text.TIMA0_IRQHandler), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.text.DL_I2C_setClockConfig), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).

271 unused section(s) (total 12931 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  puts.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    IIC.c                                    0x00000000   Number         0  iic.o ABSOLUTE
    KEY.c                                    0x00000000   Number         0  key.o ABSOLUTE
    LED.c                                    0x00000000   Number         0  led.o ABSOLUTE
    PWM.c                                    0x00000000   Number         0  pwm.o ABSOLUTE
    USART.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    hw_encoder.c                             0x00000000   Number         0  hw_encoder.o ABSOLUTE
    hw_motor.c                               0x00000000   Number         0  hw_motor.o ABSOLUTE
    software_iic.c                           0x00000000   Number         0  software_iic.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    timer.c                                  0x00000000   Number         0  timer.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_copy                           0x00000120   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x00000140   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000148   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x00000164   Section        2  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x00000166   Section       10  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x00000170   Section       10  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$00000017  0x0000017a   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0000017e   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000180   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000180   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000182   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x00000184   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x00000184   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x00000186   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x00000186   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x00000186   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0000018c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0000018c   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000190   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000190   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x00000198   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0000019a   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0000019a   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0000019e   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x000001a4   Section       48  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000001d4   Section        0  noretval__2printf.o(.text)
    .text                                    0x000001f0   Section        0  _printf_dec.o(.text)
    .text                                    0x0000025c   Section        0  __printf_ss.o(.text)
    .text                                    0x0000031c   Section        0  puts.o(.text)
    .text                                    0x00000348   Section        0  heapauxi.o(.text)
    .text                                    0x0000034e   Section        0  _printf_intcommon.o(.text)
    .text                                    0x00000400   Section        0  _printf_char_file.o(.text)
    .text                                    0x00000428   Section        0  rtudiv10.o(.text)
    _printf_input_char                       0x00000451   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x00000450   Section        0  _printf_char_common.o(.text)
    .text                                    0x00000480   Section        0  ferror.o(.text)
    .text                                    0x00000488   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x000004c6   Section        0  exit.o(.text)
    .text                                    0x000004d8   Section        8  libspace.o(.text)
    [Anonymous Symbol]                       0x000004e0   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x000004ec   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x000005e0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x000005e4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x000005e8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x000005ec   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x000005f0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    [Anonymous Symbol]                       0x000005f4   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x000006d4   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_1                             0x000006d8   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x000006dc   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_3                             0x000006e0   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    [Anonymous Symbol]                       0x000006e4   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x000006fc   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00000700   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00000714   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00000718   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00000724   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00000728   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00000740   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x00000744   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00000784   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00000788   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x0000078c   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x000007a0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x0000083c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00000840   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00000844   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00000848   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x0000084c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00000850   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00000854   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x00000858   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_8                             0x0000085c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_9                             0x00000860   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_10                            0x00000864   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_11                            0x00000868   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_12                            0x0000086c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_13                            0x00000870   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00000874   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_0                             0x000008d0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_1                             0x000008d4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_2                             0x000008d8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_3                             0x000008dc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    __arm_cp.4_5                             0x000008e0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    [Anonymous Symbol]                       0x000008e4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_0                             0x0000095c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_1                             0x00000960   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_2                             0x00000964   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_3                             0x00000968   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_4                             0x0000096c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    __arm_cp.5_5                             0x00000970   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    [Anonymous Symbol]                       0x00000974   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x000009a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x000009ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x000009b0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.9_0                             0x000009c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x000009cc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_0                             0x000009f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_1                             0x000009f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_2                             0x000009f8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_3                             0x000009fc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_4                             0x00000a00   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00000a04   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_0                             0x00000a3c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_1                             0x00000a40   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_2                             0x00000a44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_3                             0x00000a48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_4                             0x00000a4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    __arm_cp.7_5                             0x00000a50   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    [Anonymous Symbol]                       0x00000a54   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_0                             0x00000ab8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_1                             0x00000abc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_2                             0x00000ac0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_3                             0x00000ac4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_4                             0x00000ac8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_5                             0x00000acc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_6                             0x00000ad0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00000ad4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00000b10   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00000b14   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_2                             0x00000b18   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00000b1c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00000b58   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00000b5c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00000b60   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00000b64   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00000b68   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00000b6c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00000b70   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x00000b74   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x00000b78   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00000b7c   Section        0  timer.o(.text.TIMA0_IRQHandler)
    __arm_cp.3_0                             0x00000b94   Number         4  timer.o(.text.TIMA0_IRQHandler)
    [Anonymous Symbol]                       0x00000b98   Section        0  timer.o(.text.TIMG0_IRQHandler)
    __arm_cp.2_0                             0x00000bcc   Number         4  timer.o(.text.TIMG0_IRQHandler)
    __arm_cp.2_1                             0x00000bd0   Number         4  timer.o(.text.TIMG0_IRQHandler)
    __arm_cp.2_2                             0x00000bd4   Number         4  timer.o(.text.TIMG0_IRQHandler)
    __arm_cp.2_3                             0x00000bd8   Number         4  timer.o(.text.TIMG0_IRQHandler)
    __arm_cp.2_4                             0x00000bdc   Number         4  timer.o(.text.TIMG0_IRQHandler)
    [Anonymous Symbol]                       0x00000be0   Section        0  usart.o(.text.UART0_IRQHandler)
    __arm_cp.4_0                             0x00000c10   Number         4  usart.o(.text.UART0_IRQHandler)
    __arm_cp.4_2                             0x00000c14   Number         4  usart.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x00000c18   Section        0  usart.o(.text.Uart_init)
    [Anonymous Symbol]                       0x00000c28   Section        0  empty.o(.text._sys_exit)
    [Anonymous Symbol]                       0x00000c2c   Section        0  hw_encoder.o(.text.encoder_init)
    __arm_cp.0_0                             0x00000c3c   Number         4  hw_encoder.o(.text.encoder_init)
    __arm_cp.0_1                             0x00000c40   Number         4  hw_encoder.o(.text.encoder_init)
    [Anonymous Symbol]                       0x00000c44   Section        0  usart.o(.text.fputc)
    __arm_cp.2_0                             0x00000c64   Number         4  usart.o(.text.fputc)
    [Anonymous Symbol]                       0x00000c68   Section        0  hw_encoder.o(.text.get_left_encoder_count)
    [Anonymous Symbol]                       0x00000c70   Section        0  hw_encoder.o(.text.get_left_interrupt_count)
    __arm_cp.10_0                            0x00000c78   Number         4  hw_encoder.o(.text.get_left_interrupt_count)
    [Anonymous Symbol]                       0x00000c7c   Section        0  hw_encoder.o(.text.get_left_interrupt_overflow)
    __arm_cp.12_0                            0x00000c84   Number         4  hw_encoder.o(.text.get_left_interrupt_overflow)
    [Anonymous Symbol]                       0x00000c88   Section        0  hw_encoder.o(.text.left_encoder_update)
    __arm_cp.3_0                             0x00000c9c   Number         4  hw_encoder.o(.text.left_encoder_update)
    [Anonymous Symbol]                       0x00000ca0   Section        0  empty.o(.text.main)
    __arm_cp.1_0                             0x00000f24   Number         4  empty.o(.text.main)
    __arm_cp.1_1                             0x00000f28   Number         4  empty.o(.text.main)
    __arm_cp.1_2                             0x00000f2c   Number         4  empty.o(.text.main)
    __arm_cp.1_3                             0x00000f30   Number         4  empty.o(.text.main)
    __arm_cp.1_4                             0x00000f34   Number         4  empty.o(.text.main)
    __arm_cp.1_5                             0x00000f38   Number         4  empty.o(.text.main)
    __arm_cp.1_6                             0x00000f3c   Number         4  empty.o(.text.main)
    __arm_cp.1_7                             0x00000f40   Number         4  empty.o(.text.main)
    __arm_cp.1_8                             0x00000f44   Number         4  empty.o(.text.main)
    __arm_cp.1_9                             0x00000f48   Number         4  empty.o(.text.main)
    __arm_cp.1_10                            0x00000f4c   Number         4  empty.o(.text.main)
    __arm_cp.1_11                            0x00000f50   Number         4  empty.o(.text.main)
    __arm_cp.1_12                            0x00000f54   Number         4  empty.o(.text.main)
    __arm_cp.1_13                            0x00000f58   Number         4  empty.o(.text.main)
    __arm_cp.1_14                            0x00000f5c   Number         4  empty.o(.text.main)
    __arm_cp.1_15                            0x00000f60   Number         4  empty.o(.text.main)
    __arm_cp.1_16                            0x00000f64   Number         4  empty.o(.text.main)
    __arm_cp.1_17                            0x00000f68   Number         4  empty.o(.text.main)
    __arm_cp.1_18                            0x00000f6c   Number         4  empty.o(.text.main)
    __arm_cp.1_19                            0x00000f70   Number         4  empty.o(.text.main)
    __arm_cp.1_20                            0x00000f74   Number         4  empty.o(.text.main)
    __arm_cp.1_21                            0x00000f78   Number         4  empty.o(.text.main)
    __arm_cp.1_22                            0x00000f7c   Number         4  empty.o(.text.main)
    __arm_cp.1_23                            0x00000f80   Number         4  empty.o(.text.main)
    __arm_cp.1_24                            0x00000f84   Number         4  empty.o(.text.main)
    __arm_cp.1_25                            0x00000f88   Number         4  empty.o(.text.main)
    __arm_cp.1_26                            0x00000f8c   Number         4  empty.o(.text.main)
    __arm_cp.1_27                            0x00000f90   Number         4  empty.o(.text.main)
    __arm_cp.1_28                            0x00000f94   Number         4  empty.o(.text.main)
    __arm_cp.1_29                            0x00000f98   Number         4  empty.o(.text.main)
    __arm_cp.1_30                            0x00000f9c   Number         4  empty.o(.text.main)
    __arm_cp.1_35                            0x0000102c   Number         4  empty.o(.text.main)
    __arm_cp.1_36                            0x00001030   Number         4  empty.o(.text.main)
    __arm_cp.1_37                            0x00001034   Number         4  empty.o(.text.main)
    __arm_cp.1_38                            0x00001038   Number         4  empty.o(.text.main)
    __arm_cp.1_39                            0x0000103c   Number         4  empty.o(.text.main)
    __arm_cp.1_40                            0x00001040   Number         4  empty.o(.text.main)
    __arm_cp.1_41                            0x00001044   Number         4  empty.o(.text.main)
    __arm_cp.1_42                            0x00001048   Number         4  empty.o(.text.main)
    __arm_cp.1_43                            0x0000104c   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x00001050   Section        0  hw_encoder.o(.text.reset_encoders)
    __arm_cp.9_0                             0x00001070   Number         4  hw_encoder.o(.text.reset_encoders)
    __arm_cp.9_2                             0x00001074   Number         4  hw_encoder.o(.text.reset_encoders)
    __arm_cp.9_3                             0x00001078   Number         4  hw_encoder.o(.text.reset_encoders)
    [Anonymous Symbol]                       0x0000107c   Section        0  hw_encoder.o(.text.right_encoder_update)
    __arm_cp.6_0                             0x00001090   Number         4  hw_encoder.o(.text.right_encoder_update)
    [Anonymous Symbol]                       0x00001094   Section        0  hw_motor.o(.text.set_motor)
    __arm_cp.2_0                             0x000010d8   Number         4  hw_motor.o(.text.set_motor)
    __arm_cp.2_1                             0x000010dc   Number         4  hw_motor.o(.text.set_motor)
    [Anonymous Symbol]                       0x000010e0   Section        0  timer.o(.text.tim_tick_init)
    [Anonymous Symbol]                       0x000010f0   Section        0  timer.o(.text.timer0_init)
    __arm_cp.0_0                             0x00001100   Number         4  timer.o(.text.timer0_init)
    __arm_cp.0_1                             0x00001104   Number         4  timer.o(.text.timer0_init)
    gPWM_LEDClockConfig                      0x00001108   Data           3  ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig)
    [Anonymous Symbol]                       0x00001108   Section        0  ti_msp_dl_config.o(.rodata.gPWM_LEDClockConfig)
    gPWM_LEDConfig                           0x0000110c   Data           8  ti_msp_dl_config.o(.rodata.gPWM_LEDConfig)
    [Anonymous Symbol]                       0x0000110c   Section        0  ti_msp_dl_config.o(.rodata.gPWM_LEDConfig)
    gPWM_MOTORClockConfig                    0x00001114   Data           3  ti_msp_dl_config.o(.rodata.gPWM_MOTORClockConfig)
    [Anonymous Symbol]                       0x00001114   Section        0  ti_msp_dl_config.o(.rodata.gPWM_MOTORClockConfig)
    gPWM_MOTORConfig                         0x00001118   Data           8  ti_msp_dl_config.o(.rodata.gPWM_MOTORConfig)
    [Anonymous Symbol]                       0x00001118   Section        0  ti_msp_dl_config.o(.rodata.gPWM_MOTORConfig)
    gTIMER_0ClockConfig                      0x00001120   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x00001120   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x00001124   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x00001124   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gTIMER_TICKClockConfig                   0x00001138   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig)
    [Anonymous Symbol]                       0x00001138   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_TICKClockConfig)
    gTIMER_TICKTimerConfig                   0x0000113c   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig)
    [Anonymous Symbol]                       0x0000113c   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_TICKTimerConfig)
    gUART_0ClockConfig                       0x00001150   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00001150   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x00001152   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00001152   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x0000115c   Section        0  empty.o(.rodata.str1.1)
    TIMG0_IRQHandler.timer_count             0x20200000   Data           4  timer.o(.data.TIMG0_IRQHandler.timer_count)
    [Anonymous Symbol]                       0x20200000   Section        0  timer.o(.data.TIMG0_IRQHandler.timer_count)
    .bss                                     0x20200008   Section       96  libspace.o(.bss)
    Left_encoder                             0x20200068   Data          16  hw_encoder.o(.bss.Left_encoder)
    [Anonymous Symbol]                       0x20200068   Section        0  hw_encoder.o(.bss.Left_encoder)
    Right_encoder                            0x20200078   Data          16  hw_encoder.o(.bss.Right_encoder)
    [Anonymous Symbol]                       0x20200078   Section        0  hw_encoder.o(.bss.Right_encoder)
    left_interrupt_count                     0x202002dc   Data           4  hw_encoder.o(.bss.left_interrupt_count)
    [Anonymous Symbol]                       0x202002dc   Section        0  hw_encoder.o(.bss.left_interrupt_count)
    left_interrupt_overflow                  0x202002e0   Data           4  hw_encoder.o(.bss.left_interrupt_overflow)
    [Anonymous Symbol]                       0x202002e0   Section        0  hw_encoder.o(.bss.left_interrupt_overflow)
    main.loop_count                          0x202002e4   Data           4  empty.o(.bss.main.loop_count)
    [Anonymous Symbol]                       0x202002e4   Section        0  empty.o(.bss.main.loop_count)
    right_interrupt_count                    0x202002e8   Data           4  hw_encoder.o(.bss.right_interrupt_count)
    [Anonymous Symbol]                       0x202002e8   Section        0  hw_encoder.o(.bss.right_interrupt_count)
    Heap_Mem                                 0x202002f0   Data         256  startup_mspm0g350x_uvision.o(HEAP)
    HEAP                                     0x202002f0   Section      256  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x202003f0   Data         256  startup_mspm0g350x_uvision.o(STACK)
    STACK                                    0x202003f0   Section      256  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x202004f0   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x00000121   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x00000141   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000149   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x00000165   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x00000167   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x00000171   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_percent_end                      0x0000017b   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0000017f   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x00000181   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x00000183   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x00000185   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x00000187   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x00000187   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x00000187   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0000018d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0000018d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000191   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000191   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x00000199   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0000019b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0000019b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0000019f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x000001a5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000001a9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000001ab   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000001ad   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000001af   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000001b1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000001b3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    GROUP1_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000001b3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x000001b5   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __2printf                                0x000001d5   Thumb Code    22  noretval__2printf.o(.text)
    _printf_int_dec                          0x000001f1   Thumb Code    90  _printf_dec.o(.text)
    __printf                                 0x0000025d   Thumb Code   190  __printf_ss.o(.text)
    puts                                     0x0000031d   Thumb Code    38  puts.o(.text)
    __use_two_region_memory                  0x00000349   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0000034b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0000034d   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x0000034f   Thumb Code   176  _printf_intcommon.o(.text)
    _printf_char_file                        0x00000401   Thumb Code    34  _printf_char_file.o(.text)
    __rt_udiv10                              0x00000429   Thumb Code    40  rtudiv10.o(.text)
    _printf_char_common                      0x0000045b   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x00000481   Thumb Code     8  ferror.o(.text)
    __user_setup_stackheap                   0x00000489   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x000004c7   Thumb Code    16  exit.o(.text)
    __user_libspace                          0x000004d9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x000004d9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x000004d9   Thumb Code     0  libspace.o(.text)
    DL_Common_delayCycles                    0x000004e1   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_Timer_initFourCCPWMMode               0x000004ed   Thumb Code   264  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_initTimerMode                   0x000005f5   Thumb Code   240  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x000006e5   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00000701   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00000719   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00000729   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00000745   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x0000078d   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    SYSCFG_DL_GPIO_init                      0x000007a1   Thumb Code   156  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_PWM_LED_init                   0x00000875   Thumb Code    92  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_LED_init)
    SYSCFG_DL_PWM_MOTOR_init                 0x000008e5   Thumb Code   120  ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_MOTOR_init)
    SYSCFG_DL_SYSCTL_init                    0x00000975   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x000009b1   Thumb Code    24  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_TIMER_0_init                   0x000009cd   Thumb Code    36  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_TIMER_TICK_init                0x00000a05   Thumb Code    56  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_TICK_init)
    SYSCFG_DL_UART_0_init                    0x00000a55   Thumb Code   100  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x00000ad5   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00000b1d   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    TIMA0_IRQHandler                         0x00000b7d   Thumb Code    24  timer.o(.text.TIMA0_IRQHandler)
    TIMG0_IRQHandler                         0x00000b99   Thumb Code    52  timer.o(.text.TIMG0_IRQHandler)
    UART0_IRQHandler                         0x00000be1   Thumb Code    48  usart.o(.text.UART0_IRQHandler)
    Uart_init                                0x00000c19   Thumb Code    16  usart.o(.text.Uart_init)
    _sys_exit                                0x00000c29   Thumb Code     2  empty.o(.text._sys_exit)
    encoder_init                             0x00000c2d   Thumb Code    16  hw_encoder.o(.text.encoder_init)
    fputc                                    0x00000c45   Thumb Code    32  usart.o(.text.fputc)
    get_left_encoder_count                   0x00000c69   Thumb Code     8  hw_encoder.o(.text.get_left_encoder_count)
    get_left_interrupt_count                 0x00000c71   Thumb Code     8  hw_encoder.o(.text.get_left_interrupt_count)
    get_left_interrupt_overflow              0x00000c7d   Thumb Code     8  hw_encoder.o(.text.get_left_interrupt_overflow)
    left_encoder_update                      0x00000c89   Thumb Code    20  hw_encoder.o(.text.left_encoder_update)
    main                                     0x00000ca1   Thumb Code   644  empty.o(.text.main)
    reset_encoders                           0x00001051   Thumb Code    32  hw_encoder.o(.text.reset_encoders)
    right_encoder_update                     0x0000107d   Thumb Code    20  hw_encoder.o(.text.right_encoder_update)
    set_motor                                0x00001095   Thumb Code    68  hw_motor.o(.text.set_motor)
    tim_tick_init                            0x000010e1   Thumb Code    16  timer.o(.text.tim_tick_init)
    timer0_init                              0x000010f1   Thumb Code    16  timer.o(.text.timer0_init)
    Region$$Table$$Base                      0x00001628   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00001648   Number         0  anon$$obj.o(Region$$Table)
    __libspace_start                         0x20200008   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20200068   Data           0  libspace.o(.bss)
    __stdout                                 0x20200088   Data          84  empty.o(.bss.__stdout)
    gPWM_LEDBackup                           0x202000dc   Data         160  ti_msp_dl_config.o(.bss.gPWM_LEDBackup)
    gPWM_MOTORBackup                         0x2020017c   Data         160  ti_msp_dl_config.o(.bss.gPWM_MOTORBackup)
    gTIMER_TICKBackup                        0x2020021c   Data         188  ti_msp_dl_config.o(.bss.gTIMER_TICKBackup)
    led_flash_time                           0x202002d8   Data           1  timer.o(.bss.led_flash_time)
    uart_data                                0x202002ec   Data           1  usart.o(.bss.uart_data)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00001650, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00001648, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           25    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO          492  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO          680    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x0000001a   Code   RO          684    !!handler_copy      c_p.l(__scatter_copy.o)
    0x0000013a   0x0000013a   0x00000006   PAD
    0x00000140   0x00000140   0x00000002   Code   RO          681    !!handler_null      c_p.l(__scatter.o)
    0x00000142   0x00000142   0x00000006   PAD
    0x00000148   0x00000148   0x0000001c   Code   RO          686    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000164   0x00000164   0x00000002   Code   RO          475    .ARM.Collect$$_printf_percent$$00000000  c_p.l(_printf_percent.o)
    0x00000166   0x00000166   0x0000000a   Code   RO          473    .ARM.Collect$$_printf_percent$$00000009  c_p.l(_printf_d.o)
    0x00000170   0x00000170   0x0000000a   Code   RO          474    .ARM.Collect$$_printf_percent$$0000000A  c_p.l(_printf_u.o)
    0x0000017a   0x0000017a   0x00000004   Code   RO          503    .ARM.Collect$$_printf_percent$$00000017  c_p.l(_printf_percent_end.o)
    0x0000017e   0x0000017e   0x00000002   Code   RO          545    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000180   0x00000180   0x00000000   Code   RO          559    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          561    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          563    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          566    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          568    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          570    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          573    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          575    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          577    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          579    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          581    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          583    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          585    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          587    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          589    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          591    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          593    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          597    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          599    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          601    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          603    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000180   0x00000180   0x00000002   Code   RO          604    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000182   0x00000182   0x00000002   Code   RO          632    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x00000184   0x00000184   0x00000000   Code   RO          663    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000000   Code   RO          665    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000000   Code   RO          668    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000000   Code   RO          671    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000000   Code   RO          673    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000000   Code   RO          676    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x00000184   0x00000184   0x00000002   Code   RO          677    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x00000186   0x00000186   0x00000000   Code   RO          494    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x00000186   0x00000186   0x00000000   Code   RO          507    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x00000186   0x00000186   0x00000006   Code   RO          519    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x0000018c   0x0000018c   0x00000000   Code   RO          509    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x0000018c   0x0000018c   0x00000004   Code   RO          510    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000190   0x00000190   0x00000000   Code   RO          512    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000190   0x00000190   0x00000008   Code   RO          513    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x00000198   0x00000198   0x00000002   Code   RO          550    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x0000019a   0x0000019a   0x00000000   Code   RO          606    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x0000019a   0x0000019a   0x00000004   Code   RO          607    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x0000019e   0x0000019e   0x00000006   Code   RO          608    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x000001a4   0x000001a4   0x00000030   Code   RO           26    .text               startup_mspm0g350x_uvision.o
    0x000001d4   0x000001d4   0x0000001c   Code   RO          447    .text               c_p.l(noretval__2printf.o)
    0x000001f0   0x000001f0   0x0000006c   Code   RO          451    .text               c_p.l(_printf_dec.o)
    0x0000025c   0x0000025c   0x000000be   Code   RO          456    .text               c_p.l(__printf_ss.o)
    0x0000031a   0x0000031a   0x00000002   PAD
    0x0000031c   0x0000031c   0x0000002c   Code   RO          476    .text               c_p.l(puts.o)
    0x00000348   0x00000348   0x00000006   Code   RO          490    .text               c_p.l(heapauxi.o)
    0x0000034e   0x0000034e   0x000000b0   Code   RO          499    .text               c_p.l(_printf_intcommon.o)
    0x000003fe   0x000003fe   0x00000002   PAD
    0x00000400   0x00000400   0x00000028   Code   RO          501    .text               c_p.l(_printf_char_file.o)
    0x00000428   0x00000428   0x00000028   Code   RO          504    .text               c_p.l(rtudiv10.o)
    0x00000450   0x00000450   0x00000030   Code   RO          523    .text               c_p.l(_printf_char_common.o)
    0x00000480   0x00000480   0x00000008   Code   RO          525    .text               c_p.l(ferror.o)
    0x00000488   0x00000488   0x0000003e   Code   RO          529    .text               c_p.l(sys_stackheap_outer.o)
    0x000004c6   0x000004c6   0x00000010   Code   RO          534    .text               c_p.l(exit.o)
    0x000004d6   0x000004d6   0x00000002   PAD
    0x000004d8   0x000004d8   0x00000008   Code   RO          546    .text               c_p.l(libspace.o)
    0x000004e0   0x000004e0   0x0000000a   Code   RO          262    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x000004ea   0x000004ea   0x00000002   PAD
    0x000004ec   0x000004ec   0x00000108   Code   RO          383    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x000005f4   0x000005f4   0x000000f0   Code   RO          307    .text.DL_Timer_initTimerMode  driverlib.a(dl_timer.o)
    0x000006e4   0x000006e4   0x0000001c   Code   RO          349    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x00000700   0x00000700   0x00000018   Code   RO          357    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00000718   0x00000718   0x00000010   Code   RO          309    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00000728   0x00000728   0x0000001c   Code   RO          303    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x00000744   0x00000744   0x00000048   Code   RO          404    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x0000078c   0x0000078c   0x00000012   Code   RO          406    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x0000079e   0x0000079e   0x00000002   PAD
    0x000007a0   0x000007a0   0x000000d4   Code   RO           37    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00000874   0x00000874   0x00000070   Code   RO           41    .text.SYSCFG_DL_PWM_LED_init  ti_msp_dl_config.o
    0x000008e4   0x000008e4   0x00000090   Code   RO           43    .text.SYSCFG_DL_PWM_MOTOR_init  ti_msp_dl_config.o
    0x00000974   0x00000974   0x0000003c   Code   RO           39    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x000009b0   0x000009b0   0x0000001c   Code   RO           51    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x000009cc   0x000009cc   0x00000038   Code   RO           45    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00000a04   0x00000a04   0x00000050   Code   RO           47    .text.SYSCFG_DL_TIMER_TICK_init  ti_msp_dl_config.o
    0x00000a54   0x00000a54   0x00000080   Code   RO           49    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00000ad4   0x00000ad4   0x00000048   Code   RO           33    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00000b1c   0x00000b1c   0x00000060   Code   RO           35    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00000b7c   0x00000b7c   0x0000001c   Code   RO          251    .text.TIMA0_IRQHandler  timer.o
    0x00000b98   0x00000b98   0x00000048   Code   RO          249    .text.TIMG0_IRQHandler  timer.o
    0x00000be0   0x00000be0   0x00000038   Code   RO          110    .text.UART0_IRQHandler  usart.o
    0x00000c18   0x00000c18   0x00000010   Code   RO          108    .text.Uart_init     usart.o
    0x00000c28   0x00000c28   0x00000002   Code   RO            2    .text._sys_exit     empty.o
    0x00000c2a   0x00000c2a   0x00000002   PAD
    0x00000c2c   0x00000c2c   0x00000018   Code   RO          199    .text.encoder_init  hw_encoder.o
    0x00000c44   0x00000c44   0x00000024   Code   RO          106    .text.fputc         usart.o
    0x00000c68   0x00000c68   0x00000008   Code   RO          201    .text.get_left_encoder_count  hw_encoder.o
    0x00000c70   0x00000c70   0x0000000c   Code   RO          219    .text.get_left_interrupt_count  hw_encoder.o
    0x00000c7c   0x00000c7c   0x0000000c   Code   RO          223    .text.get_left_interrupt_overflow  hw_encoder.o
    0x00000c88   0x00000c88   0x00000018   Code   RO          205    .text.left_encoder_update  hw_encoder.o
    0x00000ca0   0x00000ca0   0x000003b0   Code   RO            4    .text.main          empty.o
    0x00001050   0x00001050   0x0000002c   Code   RO          217    .text.reset_encoders  hw_encoder.o
    0x0000107c   0x0000107c   0x00000018   Code   RO          211    .text.right_encoder_update  hw_encoder.o
    0x00001094   0x00001094   0x0000004c   Code   RO          187    .text.set_motor     hw_motor.o
    0x000010e0   0x000010e0   0x00000010   Code   RO          247    .text.tim_tick_init  timer.o
    0x000010f0   0x000010f0   0x00000018   Code   RO          245    .text.timer0_init   timer.o
    0x00001108   0x00001108   0x00000003   Data   RO           60    .rodata.gPWM_LEDClockConfig  ti_msp_dl_config.o
    0x0000110b   0x0000110b   0x00000001   PAD
    0x0000110c   0x0000110c   0x00000008   Data   RO           61    .rodata.gPWM_LEDConfig  ti_msp_dl_config.o
    0x00001114   0x00001114   0x00000003   Data   RO           62    .rodata.gPWM_MOTORClockConfig  ti_msp_dl_config.o
    0x00001117   0x00001117   0x00000001   PAD
    0x00001118   0x00001118   0x00000008   Data   RO           63    .rodata.gPWM_MOTORConfig  ti_msp_dl_config.o
    0x00001120   0x00001120   0x00000003   Data   RO           64    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x00001123   0x00001123   0x00000001   PAD
    0x00001124   0x00001124   0x00000014   Data   RO           65    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x00001138   0x00001138   0x00000003   Data   RO           66    .rodata.gTIMER_TICKClockConfig  ti_msp_dl_config.o
    0x0000113b   0x0000113b   0x00000001   PAD
    0x0000113c   0x0000113c   0x00000014   Data   RO           67    .rodata.gTIMER_TICKTimerConfig  ti_msp_dl_config.o
    0x00001150   0x00001150   0x00000002   Data   RO           68    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x00001152   0x00001152   0x0000000a   Data   RO           69    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x0000115c   0x0000115c   0x000004cc   Data   RO           11    .rodata.str1.1      empty.o
    0x00001628   0x00001628   0x00000020   Data   RO          679    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00001648, Size: 0x000004f0, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00001648   0x00000004   Data   RW          254    .data.TIMG0_IRQHandler.timer_count  timer.o
    0x20200004   0x0000164c   0x00000004   PAD
    0x20200008        -       0x00000060   Zero   RW          547    .bss                c_p.l(libspace.o)
    0x20200068        -       0x00000010   Zero   RW          231    .bss.Left_encoder   hw_encoder.o
    0x20200078        -       0x00000010   Zero   RW          232    .bss.Right_encoder  hw_encoder.o
    0x20200088        -       0x00000054   Zero   RW           13    .bss.__stdout       empty.o
    0x202000dc        -       0x000000a0   Zero   RW           57    .bss.gPWM_LEDBackup  ti_msp_dl_config.o
    0x2020017c        -       0x000000a0   Zero   RW           58    .bss.gPWM_MOTORBackup  ti_msp_dl_config.o
    0x2020021c        -       0x000000bc   Zero   RW           59    .bss.gTIMER_TICKBackup  ti_msp_dl_config.o
    0x202002d8        -       0x00000001   Zero   RW          253    .bss.led_flash_time  timer.o
    0x202002d9   0x0000164c   0x00000003   PAD
    0x202002dc        -       0x00000004   Zero   RW          233    .bss.left_interrupt_count  hw_encoder.o
    0x202002e0        -       0x00000004   Zero   RW          235    .bss.left_interrupt_overflow  hw_encoder.o
    0x202002e4        -       0x00000004   Zero   RW           12    .bss.main.loop_count  empty.o
    0x202002e8        -       0x00000004   Zero   RW          234    .bss.right_interrupt_count  hw_encoder.o
    0x202002ec        -       0x00000001   Zero   RW          112    .bss.uart_data      usart.o
    0x202002ed   0x0000164c   0x00000003   PAD
    0x202002f0        -       0x00000100   Zero   RW           24    HEAP                startup_mspm0g350x_uvision.o
    0x202003f0        -       0x00000100   Zero   RW           23    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x000000ff, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       946        300       1228          0         88       2134   empty.o
       148         36          0          0         44       7242   hw_encoder.o
        76          8          0          0          0       6713   hw_motor.o
        48         22        192          0        512        680   startup_mspm0g350x_uvision.o
       988        232         80          0        508      26651   ti_msp_dl_config.o
       140         32          0          4          1       5756   timer.o
       108         12          0          0          1       6604   usart.o

    ----------------------------------------------------------------------
      2456        <USER>       <GROUP>          4       1164      55780   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          4          0         10          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       190          0          0          0          0         76   __printf_ss.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         88   _printf_char_common.o
        40          6          0          0          0         72   _printf_char_file.o
        10          0          0          0          0          0   _printf_d.o
       108         18          0          0          0         76   _printf_dec.o
       176          0          0          0          0         84   _printf_intcommon.o
         2          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0          0   _printf_u.o
        16          0          0          0          0         68   exit.o
         8          0          0          0          0         60   ferror.o
         6          0          0          0          0        136   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        28          6          0          0          0         84   noretval__2printf.o
        44          6          0          0          0         68   puts.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        40          0          0          0          0         60   rtudiv10.o
        62          0          0          0          0         80   sys_stackheap_outer.o
        10          0          0          0          0        803   dl_common.o
       600        192          0          0          0      41557   dl_timer.o
        90          8          0          0          0      14163   dl_uart.o

    ----------------------------------------------------------------------
      1712        <USER>          <GROUP>          0         96      57611   Library Totals
        26          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       986         56          0          0         96       1088   c_p.l
       700        200          0          0          0      56523   driverlib.a

    ----------------------------------------------------------------------
      1712        <USER>          <GROUP>          0         96      57611   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      4168        902       1536          4       1260     112907   Grand Totals
      4168        902       1536          4       1260     112907   ELF Image Totals
      4168        902       1536          4          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 5704 (   5.57kB)
    Total RW  Size (RW Data + ZI Data)              1264 (   1.23kB)
    Total ROM Size (Code + RO Data + RW Data)       5708 (   5.57kB)

==============================================================================

